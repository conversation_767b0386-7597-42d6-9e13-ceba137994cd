#!/usr/bin/env python3
"""
测试新的智能修复架构

验证两个关键问题：
1. 智能路由决策是否真正调用了大模型
2. 自动化修复失败后是否正确传递给Aider
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bot_agent.tools.intelligent_fix_coordinator import global_intelligent_fix_coordinator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_intelligent_fix_architecture():
    """测试智能修复架构"""
    
    # 模拟flake8配置错误（与task_1748570891_1748570891相同的问题）
    test_error_analysis = {
        'general_analysis': {
            'summary': {
                'health_score': 97,
                'top_error_categories': {'lint': 1, 'job': 1},
                'error_severities': {'medium': 2},
                'critical_issues': 0,
                'recommendations_count': 2
            },
            'errors': [
                {
                    'type': 'flake8_config_error',
                    'severity': 'medium',
                    'category': 'lint',
                    'line_number': 92,
                    'content': "ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'",
                    'solutions': ['检查.flake8配置文件', '修复extend-ignore选项格式', '确保错误代码格式正确(如E203,W503)', '移除无效的错误代码']
                }
            ]
        },
        'all_errors': [
            {
                'type': 'flake8_config_error',
                'severity': 'medium',
                'category': 'lint',
                'content': "ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'",
                'solutions': ['检查.flake8配置文件', '修复extend-ignore选项格式']
            }
        ],
        'job_type': 'lint'
    }
    
    # 测试项目路径
    test_project_path = "E:\\aider-git-repos\\ai-proxy"
    test_session_id = "test_intelligent_fix_architecture"
    
    logger.info("🚀 开始测试智能修复架构...")
    logger.info("=" * 80)
    
    try:
        # 执行智能修复
        result = await global_intelligent_fix_coordinator.execute_intelligent_fix(
            error_analysis=test_error_analysis,
            project_path=test_project_path,
            session_id=test_session_id,
            job_info={'id': 'test', 'name': 'lint_test'}
        )
        
        logger.info("🎯 智能修复执行完成")
        logger.info("=" * 80)
        
        # 分析结果
        print("\n📊 执行结果分析:")
        print(f"✅ 成功状态: {result.get('success', False)}")
        print(f"📝 执行消息: {result.get('message', 'N/A')}")
        print(f"🎯 使用策略: {result.get('strategy', 'N/A')}")
        print(f"🧠 复杂度评估: {result.get('complexity', 'N/A')}")
        print(f"⏱️ 执行时间: {result.get('execution_time', 0):.2f}秒")
        
        if 'success_rate' in result:
            print(f"📈 成功率: {result['success_rate']:.1%}")
        
        # 检查是否有自动化结果
        if 'command_results' in result:
            print(f"\n🤖 自动化修复结果:")
            for i, cmd_result in enumerate(result['command_results'], 1):
                status = "✅" if cmd_result['success'] else "❌"
                print(f"  {status} 命令{i}: {cmd_result['command']}")
                if not cmd_result['success']:
                    print(f"     错误: {cmd_result.get('error', 'N/A')}")
        
        # 检查是否有Aider结果
        if 'aider_result' in result:
            print(f"\n🧠 Aider修复结果:")
            aider_result = result['aider_result']
            print(f"  ✅ 成功状态: {aider_result.get('success', False)}")
            print(f"  📝 执行消息: {aider_result.get('message', 'N/A')}")
            print(f"  ⏱️ 执行时间: {aider_result.get('execution_time', 0):.2f}秒")
        
        # 验证关键问题
        print("\n🔍 关键问题验证:")
        
        # 问题1：是否真正调用了大模型
        if result.get('strategy') in ['automated', 'aider_guided', 'hybrid']:
            print("✅ 问题1: 智能路由决策已执行")
            if 'fallback_reason' in str(result):
                print("⚠️  注意: 可能使用了fallback机制")
            else:
                print("🎯 推理模型调用成功")
        else:
            print("❌ 问题1: 智能路由决策未执行")
        
        # 问题2：自动化失败后是否传递给Aider
        if result.get('strategy') == 'automated_fallback_to_aider':
            print("✅ 问题2: 自动化失败后成功切换到Aider")
        elif result.get('strategy') == 'automated' and result.get('success_rate', 1) < 0.5:
            print("❌ 问题2: 自动化失败但未切换到Aider")
        elif result.get('strategy') == 'aider_guided':
            print("🎯 问题2: 直接使用Aider策略")
        else:
            print("ℹ️  问题2: 自动化成功，无需切换")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """主函数"""
    print("🧪 智能修复架构测试")
    print("=" * 80)
    print("测试目标:")
    print("1. 验证智能路由决策是否真正调用大模型")
    print("2. 验证自动化修复失败后是否正确传递给Aider")
    print("=" * 80)
    
    result = await test_intelligent_fix_architecture()
    
    if result:
        print("\n🎉 测试完成！")
        print("请查看上述日志分析新架构的表现。")
    else:
        print("\n💥 测试失败！")
        print("请检查错误日志并修复问题。")

if __name__ == "__main__":
    asyncio.run(main())
