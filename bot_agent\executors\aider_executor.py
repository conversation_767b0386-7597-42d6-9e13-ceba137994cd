"""
Aider执行器

职责：
- 接收Bot推理分析器生成的执行计划
- 使用Aider纯粹执行代码修改操作
- 不进行推理和决策，只执行具体指令

架构原则：
Bot推理层 → 生成执行计划 → Aider执行层（本模块）
"""

import logging
import os
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ExecutionResult:
    """执行结果"""
    success: bool
    message: str
    executed_steps: List[Dict[str, Any]]
    failed_steps: List[Dict[str, Any]]
    execution_time: float
    error_message: Optional[str] = None


class AiderExecutor:
    """
    Aider执行器

    专门负责执行Bot生成的计划，不进行推理和决策
    """

    def __init__(self):
        """初始化执行器"""
        self.aider_available = False
        self.current_session_id = None  # 当前会话ID
        self._check_aider_availability()
        logger.info("AiderExecutor initialized")

    def _check_aider_availability(self):
        """检查Aider是否可用"""
        try:
            from aider.coders import Coder
            from aider.models import Model
            from aider.io import InputOutput

            self.coder_class = Coder
            self.model_class = Model
            self.io_class = InputOutput
            self.aider_available = True
            logger.info("Aider模块可用")
        except ImportError as e:
            logger.warning(f"Aider模块不可用: {e}")
            self.aider_available = False

    async def execute_plan(self, execution_plan: Dict[str, Any], session_id: str) -> ExecutionResult:
        """
        执行Bot生成的执行计划

        Args:
            execution_plan: Bot推理分析器生成的执行计划
            session_id: 会话ID

        Returns:
            ExecutionResult: 执行结果
        """
        if not self.aider_available:
            return ExecutionResult(
                success=False,
                message="Aider不可用，无法执行计划",
                executed_steps=[],
                failed_steps=[],
                execution_time=0.0,
                error_message="Aider模块未安装或导入失败"
            )

        start_time = time.time()
        executed_steps = []
        failed_steps = []

        # 设置当前会话ID，用于监控系统
        self.current_session_id = session_id

        try:
            logger.info(f"🚀 开始执行Aider计划...")
            logger.info(f"   - 会话ID: {session_id}")
            logger.info(f"   - 任务类型: {execution_plan.get('task_type', 'unknown')}")
            logger.info(f"   - 复杂度: {execution_plan.get('complexity', 'unknown')}")

            project_path = execution_plan.get('project_path', os.getcwd())
            logger.info(f"   - 项目路径: {project_path}")

            # 修复字段名不匹配问题：智能修复协调器使用'steps'，不是'execution_steps'
            execution_steps = execution_plan.get('steps', execution_plan.get('execution_steps', []))
            logger.info(f"   - 执行步骤数量: {len(execution_steps)}")

            # 记录指令信息
            instructions = execution_plan.get('instructions', '')
            if instructions:
                logger.info(f"   - 指令长度: {len(instructions)} 字符")
                logger.info(f"   - 指令预览: {instructions[:100]}...")
            else:
                logger.warning(f"   ⚠️ 没有指令信息")

            # 创建Aider实例（带监控）
            aider_coder = await self._create_aider_instance(project_path, session_id)

            if not aider_coder:
                return ExecutionResult(
                    success=False,
                    message="无法创建Aider实例",
                    executed_steps=[],
                    failed_steps=[],
                    execution_time=time.time() - start_time,
                    error_message="Aider实例创建失败"
                )

            # 检查是否有执行步骤
            if not execution_steps:
                logger.warning("⚠️ 执行计划中没有步骤，尝试从其他字段获取指令")
                # 如果没有steps，尝试从instructions字段创建一个默认步骤
                instructions = execution_plan.get('instructions', '')
                if instructions:
                    execution_steps = [{
                        'step_id': 1,
                        'description': '执行修复指令',
                        'target': 'code_fix',
                        'instructions': instructions
                    }]
                    logger.info(f"📝 从instructions字段创建了默认执行步骤")
                else:
                    logger.error("❌ 执行计划中既没有steps也没有instructions")
                    return ExecutionResult(
                        success=False,
                        message="执行计划中没有可执行的步骤或指令",
                        executed_steps=[],
                        failed_steps=[],
                        execution_time=time.time() - start_time,
                        error_message="缺少执行步骤"
                    )

            logger.info(f"📋 准备执行 {len(execution_steps)} 个步骤")

            # 逐步执行计划
            for step in execution_steps:
                step_result = await self._execute_step(aider_coder, step, session_id)

                if step_result['success']:
                    executed_steps.append(step_result)
                    logger.info(f"✅ 步骤 {step.get('step_id')} 执行成功")
                else:
                    failed_steps.append(step_result)
                    logger.warning(f"❌ 步骤 {step.get('step_id')} 执行失败: {step_result.get('error')}")

            execution_time = time.time() - start_time
            success = len(failed_steps) == 0

            return ExecutionResult(
                success=success,
                message=f"执行完成，成功: {len(executed_steps)}, 失败: {len(failed_steps)}",
                executed_steps=executed_steps,
                failed_steps=failed_steps,
                execution_time=execution_time
            )

        except Exception as e:
            logger.error(f"执行计划失败: {e}")
            return ExecutionResult(
                success=False,
                message=f"执行计划失败: {str(e)}",
                executed_steps=executed_steps,
                failed_steps=failed_steps,
                execution_time=time.time() - start_time,
                error_message=str(e)
            )

    async def _create_aider_instance(self, project_path: str, session_id: str = None):
        """创建Aider实例（带监控）"""
        try:
            # 获取代码生成模型（不是推理模型）
            from bot_agent.config.model_config import ModelConfig
            model_name = ModelConfig.get_code_generation_model()

            logger.info(f"使用代码生成模型: {model_name}")
            model = self.model_class(model_name)

            # 设置模型温度
            try:
                if hasattr(model, 'temperature'):
                    model.temperature = 0.3
                elif hasattr(model, 'use_temperature'):
                    model.use_temperature = 0.3
            except Exception as e:
                logger.warning(f"无法设置模型温度: {e}")

            # 创建非交互式IO
            io = self.io_class(pretty=False, yes=True, chat_history_file=None)

            # 切换到项目目录
            original_cwd = os.getcwd()
            os.chdir(project_path)

            try:
                # 如果有会话ID，使用监控的Aider代理
                if session_id:
                    from bot_agent.aider_extensions.aider_monitor import aider_proxy

                    # 使用监控代理创建Coder
                    coder = aider_proxy.create_monitored_coder(
                        main_model=model,
                        io=io,
                        fnames=[],
                        use_git=True,
                        stream=False,
                        verbose=False,
                        chat_language="Chinese",
                        auto_accept_architect=True
                    )

                    # 设置监控器的会话ID
                    if hasattr(aider_proxy.monitor, 'current_session_id'):
                        aider_proxy.monitor.current_session_id = session_id

                    logger.info(f"✅ 创建了带监控的Aider实例，会话ID: {session_id}")
                else:
                    # 创建普通Coder实例
                    coder = self.coder_class.create(
                        main_model=model,
                        io=io,
                        fnames=[],
                        use_git=True,
                        stream=False,
                        verbose=False,
                        chat_language="Chinese",
                        auto_accept_architect=True
                    )

                    logger.info("✅ 创建了普通Aider实例")

                return coder

            finally:
                # 恢复原始工作目录
                os.chdir(original_cwd)

        except Exception as e:
            logger.error(f"创建Aider实例失败: {e}")
            return None

    async def _execute_step(self, aider_coder, step: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """执行单个步骤"""
        step_id = step.get('step_id', 0)
        description = step.get('description', '')
        target = step.get('target', '')

        try:
            logger.info(f"🔧 执行步骤 {step_id}: {description}")

            # 构建Aider执行指令
            execution_command = self._build_execution_command(step)
            logger.info(f"📝 Aider执行指令: {execution_command[:200]}...")

            # 不记录开始状态，避免重复记录
            # 只在执行完成后记录完整结果
            from bot_agent.utils.conversation_logger import global_conversation_logger, ConversationStatus

            # 使用Aider执行
            start_time = time.time()

            # 确保Aider实际执行命令
            if hasattr(aider_coder, 'run'):
                logger.info("🚀 调用aider_coder.run()方法")
                response = aider_coder.run(execution_command)
            elif hasattr(aider_coder, 'send_message'):
                logger.info("🚀 调用aider_coder.send_message()方法")
                response = aider_coder.send_message(execution_command)
            else:
                logger.error("❌ Aider实例没有可用的执行方法")
                raise Exception("Aider实例缺少执行方法")

            execution_time = time.time() - start_time

            logger.info(f"✅ Aider执行完成，耗时: {execution_time:.2f}秒")
            logger.info(f"📤 Aider响应: {str(response)[:300]}...")

            # 记录Aider执行结果到会话日志
            response_text = str(response) if response else "Aider执行完成，但没有返回响应内容"

            global_conversation_logger.log_round(
                session_id=session_id,
                round_name=f"Aider执行完成 {step_id} - {description}",
                prompt=f"执行指令: {execution_command[:100]}...",
                response=f"""## 🤖 Aider执行结果

### 📋 执行信息
- **步骤ID**: {step_id}
- **描述**: {description}
- **目标**: {target}
- **执行时间**: {execution_time:.2f}秒

### 🔧 执行指令
```
{execution_command}
```

### 📤 Aider响应
```
{response_text}
```

### ✅ 执行状态
执行成功，Aider已完成代码修复操作。
""",
                model_name="aider-coder",
                duration=execution_time,
                status=ConversationStatus.SUCCESS
            )

            return {
                'step_id': step_id,
                'description': description,
                'target': target,
                'success': True,
                'response': str(response),
                'execution_time': execution_time,
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"步骤 {step_id} 执行失败: {e}")
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"详细错误: {error_details}")

            # 记录Aider执行失败到会话日志
            global_conversation_logger.log_round(
                session_id=session_id,
                round_name=f"Aider执行失败 {step_id} - {description}",
                prompt=f"执行指令: {execution_command[:100]}..." if 'execution_command' in locals() else "执行指令获取失败",
                response=f"""## ❌ Aider执行失败

### 📋 执行信息
- **步骤ID**: {step_id}
- **描述**: {description}
- **目标**: {target}

### 🚨 错误信息
```
{str(e)}
```

### 📝 详细错误
```
{error_details}
```

### ❌ 执行状态
Aider执行失败，需要进一步分析和处理。
""",
                model_name="aider-coder",
                duration=0.0,
                status=ConversationStatus.FAILED,
                error_message=str(e)
            )

            return {
                'step_id': step_id,
                'description': description,
                'target': target,
                'success': False,
                'error': str(e),
                'execution_time': 0.0,
                'timestamp': time.time()
            }

    def _build_execution_command(self, step: Dict[str, Any]) -> str:
        """构建Aider执行命令"""
        action = step.get('action', 'code_fix')
        target = step.get('target', '')
        description = step.get('description', '')

        if action == 'code_fix':
            return f"""
请修复以下错误：{target}

修复要求：
- 直接修复代码，不要询问
- 保持代码风格一致
- 确保修复后代码能正常运行
- 用中文简要说明修复内容

开始修复：{description}
"""
        elif action == 'file_create':
            return f"""
请创建文件：{target}

创建要求：
- 按照描述创建文件
- 确保文件格式正确
- 用中文说明创建内容

创建描述：{description}
"""
        elif action == 'file_modify':
            return f"""
请修改文件：{target}

修改要求：
- 按照描述修改文件
- 保持原有结构
- 用中文说明修改内容

修改描述：{description}
"""
        else:
            return f"""
请执行以下操作：{description}

目标：{target}
操作类型：{action}

请直接执行，用中文说明执行过程。
"""


# 全局实例
global_aider_executor = AiderExecutor()
