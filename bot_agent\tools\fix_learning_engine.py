"""
修复学习引擎 - 从修复历史中学习和优化

核心功能：
1. 记录修复模式和成功率
2. 学习最佳修复策略
3. 优化路由决策
4. 提供修复建议
"""

import logging
import json
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict
import os

logger = logging.getLogger(__name__)

@dataclass
class FixPattern:
    """修复模式"""
    error_signature: str          # 错误特征签名
    fix_strategy: str            # 修复策略
    success_count: int           # 成功次数
    failure_count: int           # 失败次数
    avg_execution_time: float    # 平均执行时间
    last_used: float            # 最后使用时间
    complexity_score: float      # 复杂度评分
    commands_used: List[str]     # 使用的命令
    context_factors: Dict[str, Any]  # 上下文因素

class FixLearningEngine:
    """修复学习引擎"""
    
    def __init__(self, learning_data_path: str = "data/fix_learning.json"):
        self.learning_data_path = learning_data_path
        self.patterns = {}
        self.load_learning_data()
    
    def load_learning_data(self):
        """加载学习数据"""
        try:
            if os.path.exists(self.learning_data_path):
                with open(self.learning_data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for signature, pattern_data in data.items():
                        self.patterns[signature] = FixPattern(**pattern_data)
                logger.info(f"✅ 加载了 {len(self.patterns)} 个修复模式")
            else:
                logger.info("📝 创建新的学习数据文件")
                os.makedirs(os.path.dirname(self.learning_data_path), exist_ok=True)
        except Exception as e:
            logger.error(f"加载学习数据失败: {e}")
            self.patterns = {}
    
    def save_learning_data(self):
        """保存学习数据"""
        try:
            data = {signature: asdict(pattern) for signature, pattern in self.patterns.items()}
            with open(self.learning_data_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"💾 保存了 {len(self.patterns)} 个修复模式")
        except Exception as e:
            logger.error(f"保存学习数据失败: {e}")
    
    def generate_error_signature(self, error_analysis: Dict[str, Any]) -> str:
        """生成错误特征签名"""
        try:
            # 提取关键特征
            job_type = error_analysis.get('job_type', 'unknown')
            error_types = []
            error_keywords = []
            
            for error in error_analysis.get('all_errors', []):
                if isinstance(error, dict):
                    error_types.append(error.get('type', 'unknown'))
                    content = str(error.get('content', ''))
                    # 提取关键词
                    if 'flake8' in content.lower():
                        error_keywords.append('flake8')
                    if 'extend-ignore' in content.lower():
                        error_keywords.append('extend-ignore')
                    if 'black' in content.lower():
                        error_keywords.append('black')
                    if 'pytest' in content.lower():
                        error_keywords.append('pytest')
            
            # 生成签名
            signature_parts = [
                f"job:{job_type}",
                f"types:{','.join(sorted(set(error_types)))}",
                f"keywords:{','.join(sorted(set(error_keywords)))}"
            ]
            
            return "|".join(signature_parts)
            
        except Exception as e:
            logger.error(f"生成错误签名失败: {e}")
            return "unknown"
    
    def record_fix_attempt(self, 
                          error_analysis: Dict[str, Any],
                          fix_decision: Dict[str, Any],
                          fix_result: Dict[str, Any],
                          validation_result: Optional[Dict[str, Any]] = None):
        """记录修复尝试"""
        try:
            signature = self.generate_error_signature(error_analysis)
            strategy = fix_decision.get('strategy', 'unknown')
            success = fix_result.get('success', False)
            execution_time = fix_result.get('execution_time', 0)
            complexity = fix_decision.get('complexity', 'unknown')
            
            # 提取使用的命令
            commands_used = []
            if 'command_results' in fix_result:
                commands_used = [cmd.get('command', '') for cmd in fix_result['command_results']]
            elif 'automated_commands' in fix_decision:
                commands_used = fix_decision['automated_commands']
            
            # 提取上下文因素
            context_factors = {
                'project_type': error_analysis.get('project_type', 'unknown'),
                'error_count': len(error_analysis.get('all_errors', [])),
                'has_config_files': self._detect_config_files(error_analysis)
            }
            
            # 更新或创建模式
            if signature in self.patterns:
                pattern = self.patterns[signature]
                if success:
                    pattern.success_count += 1
                else:
                    pattern.failure_count += 1
                
                # 更新平均执行时间
                total_attempts = pattern.success_count + pattern.failure_count
                pattern.avg_execution_time = (
                    (pattern.avg_execution_time * (total_attempts - 1) + execution_time) / total_attempts
                )
                pattern.last_used = time.time()
                
                # 更新最佳策略
                if success and strategy != pattern.fix_strategy:
                    # 如果新策略成功，考虑更新
                    current_success_rate = pattern.success_count / total_attempts
                    if current_success_rate < 0.7:  # 当前策略成功率不高时才更新
                        pattern.fix_strategy = strategy
                
            else:
                # 创建新模式
                self.patterns[signature] = FixPattern(
                    error_signature=signature,
                    fix_strategy=strategy,
                    success_count=1 if success else 0,
                    failure_count=0 if success else 1,
                    avg_execution_time=execution_time,
                    last_used=time.time(),
                    complexity_score=self._parse_complexity_score(complexity),
                    commands_used=commands_used,
                    context_factors=context_factors
                )
            
            # 保存学习数据
            self.save_learning_data()
            
            logger.info(f"📚 记录修复尝试: {signature} -> {strategy} ({'成功' if success else '失败'})")
            
        except Exception as e:
            logger.error(f"记录修复尝试失败: {e}")
    
    def get_recommended_strategy(self, error_analysis: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取推荐的修复策略"""
        try:
            signature = self.generate_error_signature(error_analysis)
            
            # 精确匹配
            if signature in self.patterns:
                pattern = self.patterns[signature]
                total_attempts = pattern.success_count + pattern.failure_count
                success_rate = pattern.success_count / total_attempts if total_attempts > 0 else 0
                
                if success_rate > 0.6 and total_attempts >= 3:  # 有足够的历史数据且成功率较高
                    return {
                        'recommended_strategy': pattern.fix_strategy,
                        'confidence': success_rate,
                        'reasoning': f'基于历史数据，该策略成功率为 {success_rate:.1%} ({pattern.success_count}/{total_attempts})',
                        'estimated_time': pattern.avg_execution_time,
                        'suggested_commands': pattern.commands_used,
                        'learning_source': 'exact_match'
                    }
            
            # 模糊匹配 - 查找相似的错误模式
            similar_patterns = self._find_similar_patterns(error_analysis)
            if similar_patterns:
                best_pattern = max(similar_patterns, key=lambda p: p['similarity_score'] * p['success_rate'])
                
                if best_pattern['success_rate'] > 0.5:
                    return {
                        'recommended_strategy': best_pattern['strategy'],
                        'confidence': best_pattern['success_rate'] * best_pattern['similarity_score'],
                        'reasoning': f'基于相似错误模式，相似度 {best_pattern["similarity_score"]:.1%}，成功率 {best_pattern["success_rate"]:.1%}',
                        'estimated_time': best_pattern['avg_time'],
                        'learning_source': 'similar_match'
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"获取推荐策略失败: {e}")
            return None
    
    def _find_similar_patterns(self, error_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查找相似的错误模式"""
        similar_patterns = []
        
        try:
            target_job_type = error_analysis.get('job_type', 'unknown')
            target_error_types = set()
            target_keywords = set()
            
            for error in error_analysis.get('all_errors', []):
                if isinstance(error, dict):
                    target_error_types.add(error.get('type', 'unknown'))
                    content = str(error.get('content', '')).lower()
                    if 'flake8' in content:
                        target_keywords.add('flake8')
                    if 'black' in content:
                        target_keywords.add('black')
                    if 'pytest' in content:
                        target_keywords.add('pytest')
            
            for signature, pattern in self.patterns.items():
                # 解析模式特征
                parts = signature.split('|')
                pattern_job_type = parts[0].split(':')[1] if len(parts) > 0 else 'unknown'
                pattern_error_types = set(parts[1].split(':')[1].split(',')) if len(parts) > 1 else set()
                pattern_keywords = set(parts[2].split(':')[1].split(',')) if len(parts) > 2 else set()
                
                # 计算相似度
                similarity_score = 0.0
                
                # 作业类型匹配
                if target_job_type == pattern_job_type:
                    similarity_score += 0.4
                
                # 错误类型匹配
                error_type_overlap = len(target_error_types & pattern_error_types)
                error_type_union = len(target_error_types | pattern_error_types)
                if error_type_union > 0:
                    similarity_score += 0.3 * (error_type_overlap / error_type_union)
                
                # 关键词匹配
                keyword_overlap = len(target_keywords & pattern_keywords)
                keyword_union = len(target_keywords | pattern_keywords)
                if keyword_union > 0:
                    similarity_score += 0.3 * (keyword_overlap / keyword_union)
                
                # 只考虑相似度较高的模式
                if similarity_score > 0.5:
                    total_attempts = pattern.success_count + pattern.failure_count
                    success_rate = pattern.success_count / total_attempts if total_attempts > 0 else 0
                    
                    similar_patterns.append({
                        'strategy': pattern.fix_strategy,
                        'similarity_score': similarity_score,
                        'success_rate': success_rate,
                        'avg_time': pattern.avg_execution_time,
                        'total_attempts': total_attempts
                    })
            
        except Exception as e:
            logger.error(f"查找相似模式失败: {e}")
        
        return similar_patterns
    
    def _detect_config_files(self, error_analysis: Dict[str, Any]) -> List[str]:
        """检测配置文件"""
        config_files = []
        # 这里可以根据错误分析检测相关的配置文件
        return config_files
    
    def _parse_complexity_score(self, complexity: str) -> float:
        """解析复杂度评分"""
        complexity_map = {
            'simple': 0.2,
            'moderate': 0.5,
            'complex': 0.8,
            'creative': 1.0
        }
        return complexity_map.get(complexity, 0.5)
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """获取学习统计信息"""
        try:
            total_patterns = len(self.patterns)
            total_attempts = sum(p.success_count + p.failure_count for p in self.patterns.values())
            total_successes = sum(p.success_count for p in self.patterns.values())
            
            strategy_stats = defaultdict(lambda: {'success': 0, 'failure': 0})
            for pattern in self.patterns.values():
                strategy_stats[pattern.fix_strategy]['success'] += pattern.success_count
                strategy_stats[pattern.fix_strategy]['failure'] += pattern.failure_count
            
            return {
                'total_patterns': total_patterns,
                'total_attempts': total_attempts,
                'overall_success_rate': total_successes / total_attempts if total_attempts > 0 else 0,
                'strategy_performance': dict(strategy_stats),
                'most_common_errors': self._get_most_common_errors(),
                'learning_data_path': self.learning_data_path
            }
            
        except Exception as e:
            logger.error(f"获取学习统计失败: {e}")
            return {}
    
    def _get_most_common_errors(self) -> List[Dict[str, Any]]:
        """获取最常见的错误"""
        error_counts = defaultdict(int)
        
        for pattern in self.patterns.values():
            total_attempts = pattern.success_count + pattern.failure_count
            error_counts[pattern.error_signature] += total_attempts
        
        # 按出现次数排序
        sorted_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)
        
        return [
            {
                'error_signature': signature,
                'occurrence_count': count,
                'success_rate': self.patterns[signature].success_count / (self.patterns[signature].success_count + self.patterns[signature].failure_count)
            }
            for signature, count in sorted_errors[:10]  # 返回前10个
        ]


# 全局学习引擎实例
global_fix_learning_engine = FixLearningEngine()
