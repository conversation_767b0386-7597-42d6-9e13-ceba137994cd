"""
错误分类器 - 智能分类错误并确定优先级

核心功能：
1. 智能分类错误类型
2. 评估错误优先级和影响范围
3. 提供修复建议和策略
4. 支持多种编程语言和框架
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import re

logger = logging.getLogger(__name__)

class ErrorPriority(Enum):
    """错误优先级枚举"""
    CRITICAL = "critical"    # 阻塞性错误，必须立即修复
    HIGH = "high"           # 重要错误，影响核心功能
    MEDIUM = "medium"       # 一般错误，影响部分功能
    LOW = "low"            # 警告性错误，不影响功能

class ErrorCategory(Enum):
    """错误类别枚举"""
    SYNTAX = "syntax"                    # 语法错误
    IMPORT = "import"                    # 导入错误
    DEPENDENCY = "dependency"            # 依赖错误
    CONFIGURATION = "configuration"     # 配置错误
    LINT = "lint"                       # 代码规范错误
    TEST = "test"                       # 测试错误
    BUILD = "build"                     # 构建错误
    RUNTIME = "runtime"                 # 运行时错误
    SECURITY = "security"               # 安全错误
    PERFORMANCE = "performance"         # 性能错误

class ErrorImpact(Enum):
    """错误影响范围枚举"""
    BLOCKING = "blocking"       # 阻塞整个流程
    FUNCTIONAL = "functional"   # 影响功能
    QUALITY = "quality"        # 影响代码质量
    COSMETIC = "cosmetic"      # 仅影响外观

@dataclass
class ClassifiedError:
    """分类后的错误"""
    original_error: Any
    category: ErrorCategory
    priority: ErrorPriority
    impact: ErrorImpact
    confidence: float
    description: str
    affected_files: List[str]
    suggested_fix_strategy: str
    estimated_fix_time: int  # 预估修复时间（秒）
    dependencies: List[str]  # 依赖的其他修复

class ErrorClassifier:
    """错误分类器"""
    
    def __init__(self):
        self.classification_patterns = self._build_classification_patterns()
    
    def classify_errors(self, errors: List[Any], project_context: Dict[str, Any] = None) -> List[ClassifiedError]:
        """
        分类错误列表
        
        Args:
            errors: 错误列表
            project_context: 项目上下文信息
            
        Returns:
            List[ClassifiedError]: 分类后的错误列表
        """
        try:
            classified_errors = []
            
            for error in errors:
                classified = self._classify_single_error(error, project_context)
                if classified:
                    classified_errors.append(classified)
            
            # 按优先级排序
            classified_errors.sort(key=lambda x: self._get_priority_weight(x.priority), reverse=True)
            
            # 分析错误依赖关系
            self._analyze_error_dependencies(classified_errors)
            
            logger.info(f"✅ 成功分类 {len(classified_errors)} 个错误")
            return classified_errors
            
        except Exception as e:
            logger.error(f"错误分类失败: {e}")
            return []
    
    def _classify_single_error(self, error: Any, project_context: Dict[str, Any] = None) -> Optional[ClassifiedError]:
        """分类单个错误"""
        try:
            error_text = str(error)
            if isinstance(error, dict):
                error_text = error.get('content', '') or error.get('message', '') or str(error)
            
            # 使用模式匹配进行分类
            for pattern_info in self.classification_patterns:
                if self._match_pattern(error_text, pattern_info['patterns']):
                    return ClassifiedError(
                        original_error=error,
                        category=pattern_info['category'],
                        priority=pattern_info['priority'],
                        impact=pattern_info['impact'],
                        confidence=pattern_info['confidence'],
                        description=pattern_info['description'],
                        affected_files=self._extract_affected_files(error_text),
                        suggested_fix_strategy=pattern_info['fix_strategy'],
                        estimated_fix_time=pattern_info['estimated_time'],
                        dependencies=[]
                    )
            
            # 如果没有匹配到模式，使用默认分类
            return ClassifiedError(
                original_error=error,
                category=ErrorCategory.RUNTIME,
                priority=ErrorPriority.MEDIUM,
                impact=ErrorImpact.FUNCTIONAL,
                confidence=0.5,
                description="未分类错误",
                affected_files=[],
                suggested_fix_strategy="manual_analysis",
                estimated_fix_time=300,
                dependencies=[]
            )
            
        except Exception as e:
            logger.error(f"分类单个错误失败: {e}")
            return None
    
    def _build_classification_patterns(self) -> List[Dict[str, Any]]:
        """构建分类模式"""
        return [
            # 语法错误
            {
                'patterns': [r'SyntaxError', r'IndentationError', r'TabError', r'invalid syntax'],
                'category': ErrorCategory.SYNTAX,
                'priority': ErrorPriority.CRITICAL,
                'impact': ErrorImpact.BLOCKING,
                'confidence': 0.95,
                'description': 'Python语法错误',
                'fix_strategy': 'aider_guided',
                'estimated_time': 120
            },
            
            # 导入错误
            {
                'patterns': [r'ImportError', r'ModuleNotFoundError', r'No module named', r'cannot import'],
                'category': ErrorCategory.IMPORT,
                'priority': ErrorPriority.HIGH,
                'impact': ErrorImpact.BLOCKING,
                'confidence': 0.9,
                'description': '模块导入错误',
                'fix_strategy': 'automated',
                'estimated_time': 60
            },
            
            # 依赖错误
            {
                'patterns': [r'pip install', r'requirements\.txt', r'package not found', r'dependency'],
                'category': ErrorCategory.DEPENDENCY,
                'priority': ErrorPriority.HIGH,
                'impact': ErrorImpact.BLOCKING,
                'confidence': 0.85,
                'description': '依赖包缺失',
                'fix_strategy': 'automated',
                'estimated_time': 90
            },
            
            # 配置错误
            {
                'patterns': [r'flake8.*config', r'\.flake8', r'extend-ignore', r'configuration error'],
                'category': ErrorCategory.CONFIGURATION,
                'priority': ErrorPriority.MEDIUM,
                'impact': ErrorImpact.QUALITY,
                'confidence': 0.8,
                'description': '配置文件错误',
                'fix_strategy': 'automated',
                'estimated_time': 45
            },
            
            # 代码规范错误
            {
                'patterns': [r'flake8', r'black', r'E\d{3}', r'W\d{3}', r'F\d{3}', r'line too long'],
                'category': ErrorCategory.LINT,
                'priority': ErrorPriority.LOW,
                'impact': ErrorImpact.QUALITY,
                'confidence': 0.9,
                'description': '代码规范问题',
                'fix_strategy': 'automated',
                'estimated_time': 30
            },
            
            # 测试错误
            {
                'patterns': [r'pytest', r'test.*failed', r'AssertionError', r'test case'],
                'category': ErrorCategory.TEST,
                'priority': ErrorPriority.HIGH,
                'impact': ErrorImpact.FUNCTIONAL,
                'confidence': 0.85,
                'description': '测试失败',
                'fix_strategy': 'aider_guided',
                'estimated_time': 240
            },
            
            # 构建错误
            {
                'patterns': [r'build.*failed', r'compilation error', r'make.*error'],
                'category': ErrorCategory.BUILD,
                'priority': ErrorPriority.CRITICAL,
                'impact': ErrorImpact.BLOCKING,
                'confidence': 0.8,
                'description': '构建失败',
                'fix_strategy': 'aider_guided',
                'estimated_time': 300
            },
            
            # 安全错误
            {
                'patterns': [r'security', r'vulnerability', r'CVE-', r'unsafe'],
                'category': ErrorCategory.SECURITY,
                'priority': ErrorPriority.CRITICAL,
                'impact': ErrorImpact.BLOCKING,
                'confidence': 0.9,
                'description': '安全漏洞',
                'fix_strategy': 'aider_guided',
                'estimated_time': 600
            },
            
            # 性能错误
            {
                'patterns': [r'timeout', r'performance', r'slow', r'memory.*leak'],
                'category': ErrorCategory.PERFORMANCE,
                'priority': ErrorPriority.MEDIUM,
                'impact': ErrorImpact.FUNCTIONAL,
                'confidence': 0.7,
                'description': '性能问题',
                'fix_strategy': 'aider_guided',
                'estimated_time': 480
            }
        ]
    
    def _match_pattern(self, text: str, patterns: List[str]) -> bool:
        """匹配模式"""
        text_lower = text.lower()
        for pattern in patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        return False
    
    def _extract_affected_files(self, error_text: str) -> List[str]:
        """提取受影响的文件"""
        files = []
        
        # 匹配文件路径模式
        file_patterns = [
            r'File "([^"]+)"',
            r'in file ([^\s]+)',
            r'([^\s]+\.py):\d+',
            r'([^\s]+\.js):\d+',
            r'([^\s]+\.ts):\d+',
        ]
        
        for pattern in file_patterns:
            matches = re.findall(pattern, error_text)
            files.extend(matches)
        
        return list(set(files))  # 去重
    
    def _get_priority_weight(self, priority: ErrorPriority) -> int:
        """获取优先级权重"""
        weights = {
            ErrorPriority.CRITICAL: 4,
            ErrorPriority.HIGH: 3,
            ErrorPriority.MEDIUM: 2,
            ErrorPriority.LOW: 1
        }
        return weights.get(priority, 0)
    
    def _analyze_error_dependencies(self, classified_errors: List[ClassifiedError]):
        """分析错误依赖关系"""
        try:
            for i, error in enumerate(classified_errors):
                dependencies = []
                
                # 语法错误通常需要先修复
                if error.category == ErrorCategory.SYNTAX:
                    continue  # 语法错误优先级最高，不依赖其他修复
                
                # 导入错误可能依赖依赖包安装
                if error.category == ErrorCategory.IMPORT:
                    for other_error in classified_errors:
                        if (other_error.category == ErrorCategory.DEPENDENCY and 
                            any(file in error.affected_files for file in other_error.affected_files)):
                            dependencies.append(f"dependency_{classified_errors.index(other_error)}")
                
                # 测试错误可能依赖语法和导入错误的修复
                if error.category == ErrorCategory.TEST:
                    for other_error in classified_errors:
                        if other_error.category in [ErrorCategory.SYNTAX, ErrorCategory.IMPORT]:
                            dependencies.append(f"error_{classified_errors.index(other_error)}")
                
                error.dependencies = dependencies
                
        except Exception as e:
            logger.error(f"分析错误依赖关系失败: {e}")
    
    def get_fix_order(self, classified_errors: List[ClassifiedError]) -> List[ClassifiedError]:
        """获取修复顺序"""
        try:
            # 按优先级和依赖关系排序
            ordered_errors = []
            remaining_errors = classified_errors.copy()
            
            while remaining_errors:
                # 找到没有未满足依赖的错误
                ready_errors = []
                for error in remaining_errors:
                    if not error.dependencies or all(
                        dep not in [f"error_{remaining_errors.index(e)}" for e in remaining_errors]
                        for dep in error.dependencies
                    ):
                        ready_errors.append(error)
                
                if not ready_errors:
                    # 如果没有准备好的错误，选择优先级最高的
                    ready_errors = [max(remaining_errors, key=lambda x: self._get_priority_weight(x.priority))]
                
                # 按优先级排序准备好的错误
                ready_errors.sort(key=lambda x: self._get_priority_weight(x.priority), reverse=True)
                
                # 添加到有序列表
                for error in ready_errors:
                    ordered_errors.append(error)
                    remaining_errors.remove(error)
            
            return ordered_errors
            
        except Exception as e:
            logger.error(f"获取修复顺序失败: {e}")
            return classified_errors
    
    def get_classification_summary(self, classified_errors: List[ClassifiedError]) -> Dict[str, Any]:
        """获取分类摘要"""
        try:
            summary = {
                'total_errors': len(classified_errors),
                'by_priority': {},
                'by_category': {},
                'by_impact': {},
                'estimated_total_time': 0,
                'critical_errors': [],
                'blocking_errors': []
            }
            
            for error in classified_errors:
                # 按优先级统计
                priority_key = error.priority.value
                summary['by_priority'][priority_key] = summary['by_priority'].get(priority_key, 0) + 1
                
                # 按类别统计
                category_key = error.category.value
                summary['by_category'][category_key] = summary['by_category'].get(category_key, 0) + 1
                
                # 按影响统计
                impact_key = error.impact.value
                summary['by_impact'][impact_key] = summary['by_impact'].get(impact_key, 0) + 1
                
                # 累计预估时间
                summary['estimated_total_time'] += error.estimated_fix_time
                
                # 收集关键错误
                if error.priority == ErrorPriority.CRITICAL:
                    summary['critical_errors'].append(error.description)
                
                if error.impact == ErrorImpact.BLOCKING:
                    summary['blocking_errors'].append(error.description)
            
            return summary
            
        except Exception as e:
            logger.error(f"获取分类摘要失败: {e}")
            return {'error': str(e)}


# 全局错误分类器实例
global_error_classifier = ErrorClassifier()
