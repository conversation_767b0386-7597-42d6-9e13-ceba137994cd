#!/usr/bin/env python3
"""
直接测试清空调试日志功能（不依赖Web UI）
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_clear_debug_logs_direct():
    try:
        print('🔍 直接测试清空调试日志功能...')

        # 导入调试日志模块
        from bot_agent.utils.debug_logger import global_debug_logger, DebugLogCategory, DebugLogLevel
        import time

        # 检查当前日志数量
        before_logs = global_debug_logger.get_recent_logs(minutes=60)
        print(f'📊 当前日志数量: {len(before_logs)}')

        if len(before_logs) > 0:
            print('📋 当前日志:')
            for i, log in enumerate(before_logs[-5:], 1):  # 显示最后5条
                print(f'  {i}. [{log["level"]}] {log["component"]}: {log["message"]}')

        # 添加一条测试日志
        print('\n📝 添加测试日志...')
        session_id = f"test_clear_{int(time.time())}"
        global_debug_logger.log(session_id, DebugLogCategory.SYSTEM, DebugLogLevel.INFO,
                               'TestClearFunction', '测试清空功能', {'test': True})

        after_add_logs = global_debug_logger.get_recent_logs(minutes=60)
        print(f'📊 添加后日志数量: {len(after_add_logs)}')

        # 直接调用清空函数
        print('\n🗑️ 直接调用清空调试日志函数...')

        # 导入清空函数
        from web_ui.conversation_viewer_app import clear_debug_logs
        import asyncio

        # 执行清空
        cleared_count = asyncio.run(clear_debug_logs(days_to_keep=0))
        print(f'✅ 清空函数返回: 清空了 {cleared_count} 条日志')

        # 检查清空后的状态
        after_clear_logs = global_debug_logger.get_recent_logs(minutes=60)
        print(f'📊 清空后日志数量: {len(after_clear_logs)}')

        # 检查文件是否被删除
        log_file_path = global_debug_logger.log_file
        print(f'📁 日志文件路径: {log_file_path}')
        print(f'📄 文件是否存在: {log_file_path.exists()}')

        if len(after_clear_logs) == 0 and not log_file_path.exists():
            print('✅ 调试日志清空成功！内存和文件都已清空')
        elif len(after_clear_logs) == 0:
            print('⚠️ 内存中的日志已清空，但文件可能仍存在')
        else:
            print(f'❌ 清空失败，还剩 {len(after_clear_logs)} 条日志')

        # 测试"全部日志"清空逻辑
        print('\n🔍 测试"全部日志"清空逻辑...')

        # 重新添加日志
        global_debug_logger.log(session_id + "_all", DebugLogCategory.ROUTER, DebugLogLevel.WARNING,
                               'TestAllClear', '测试全部清空', {'test_all': True})

        before_all_logs = global_debug_logger.get_recent_logs(minutes=60)
        print(f'📊 重新添加后日志数量: {len(before_all_logs)}')

        # 模拟"全部日志"清空（包含调试日志）
        print('🗑️ 模拟全部日志清空...')
        cleared_count_all = asyncio.run(clear_debug_logs(days_to_keep=0))
        print(f'✅ 全部清空返回: 清空了 {cleared_count_all} 条调试日志')

        after_all_logs = global_debug_logger.get_recent_logs(minutes=60)
        print(f'📊 全部清空后日志数量: {len(after_all_logs)}')

        if len(after_all_logs) == 0:
            print('✅ 全部日志清空成功！')
        else:
            print(f'❌ 全部日志清空失败，还剩 {len(after_all_logs)} 条')

    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_clear_debug_logs_direct()
