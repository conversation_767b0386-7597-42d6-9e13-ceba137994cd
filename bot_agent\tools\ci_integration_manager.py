"""
CI集成管理器 - 深度集成CI/CD流程和自动化测试

核心功能：
1. CI/CD流程集成
2. 自动化测试验证
3. 回归测试管理
4. 部署前验证
"""

import logging
import asyncio
import subprocess
import os
import yaml
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import time

logger = logging.getLogger(__name__)

class CIProvider(Enum):
    """CI提供商"""
    GITLAB = "gitlab"
    GITHUB = "github"
    JENKINS = "jenkins"
    AZURE = "azure"
    CUSTOM = "custom"

class TestType(Enum):
    """测试类型"""
    UNIT = "unit"
    INTEGRATION = "integration"
    E2E = "e2e"
    PERFORMANCE = "performance"
    SECURITY = "security"
    REGRESSION = "regression"

@dataclass
class TestResult:
    """测试结果"""
    test_type: TestType
    success: bool
    duration: float
    coverage: Optional[float]
    failed_tests: List[str]
    output: str
    error_message: Optional[str] = None

@dataclass
class CIJob:
    """CI作业"""
    job_id: str
    project_id: str
    branch: str
    commit_hash: str
    trigger_type: str  # push, merge_request, manual
    status: str  # pending, running, success, failed
    test_results: List[TestResult]
    fix_applied: bool
    created_at: float
    completed_at: Optional[float] = None

class CIIntegrationManager:
    """CI集成管理器"""
    
    def __init__(self):
        self.active_jobs = {}
        self.test_templates = {}
        self.regression_tests = {}
        self.ci_configs = {}
    
    async def setup_ci_integration(self, 
                                 project_id: str,
                                 ci_provider: CIProvider,
                                 config: Dict[str, Any]) -> bool:
        """设置CI集成"""
        try:
            self.ci_configs[project_id] = {
                'provider': ci_provider.value,
                'config': config,
                'webhook_url': config.get('webhook_url'),
                'api_token': config.get('api_token'),
                'project_url': config.get('project_url')
            }
            
            # 生成CI配置文件
            await self._generate_ci_config(project_id, ci_provider, config)
            
            logger.info(f"✅ 设置CI集成: {project_id} -> {ci_provider.value}")
            return True
            
        except Exception as e:
            logger.error(f"设置CI集成失败: {e}")
            return False
    
    async def _generate_ci_config(self, 
                                project_id: str,
                                ci_provider: CIProvider,
                                config: Dict[str, Any]):
        """生成CI配置文件"""
        try:
            project_path = config.get('project_path', '.')
            
            if ci_provider == CIProvider.GITLAB:
                await self._generate_gitlab_ci(project_path, config)
            elif ci_provider == CIProvider.GITHUB:
                await self._generate_github_actions(project_path, config)
            
        except Exception as e:
            logger.error(f"生成CI配置失败: {e}")
    
    async def _generate_gitlab_ci(self, project_path: str, config: Dict[str, Any]):
        """生成GitLab CI配置"""
        gitlab_ci_content = {
            'stages': ['test', 'quality', 'fix', 'deploy'],
            'variables': {
                'PIP_CACHE_DIR': '$CI_PROJECT_DIR/.cache/pip'
            },
            'cache': {
                'paths': ['.cache/pip', 'venv/']
            },
            'before_script': [
                'python -m venv venv',
                'source venv/bin/activate',
                'pip install --upgrade pip',
                'pip install -r requirements.txt'
            ],
            'test:unit': {
                'stage': 'test',
                'script': [
                    'pytest tests/ --cov=. --cov-report=xml --cov-report=term',
                    'coverage report --fail-under=80'
                ],
                'artifacts': {
                    'reports': {
                        'coverage_report': {
                            'coverage_format': 'cobertura',
                            'path': 'coverage.xml'
                        }
                    }
                }
            },
            'quality:check': {
                'stage': 'quality',
                'script': [
                    'flake8 .',
                    'black --check .',
                    'bandit -r . -f json -o bandit-report.json'
                ],
                'artifacts': {
                    'reports': {
                        'codequality': 'bandit-report.json'
                    }
                },
                'allow_failure': True
            },
            'fix:auto': {
                'stage': 'fix',
                'script': [
                    'python -m bot_agent.tools.ci_fix_trigger --project-id=$CI_PROJECT_ID --commit=$CI_COMMIT_SHA'
                ],
                'when': 'on_failure',
                'only': ['merge_requests']
            }
        }
        
        ci_file_path = os.path.join(project_path, '.gitlab-ci.yml')
        with open(ci_file_path, 'w', encoding='utf-8') as f:
            yaml.dump(gitlab_ci_content, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"✅ 生成GitLab CI配置: {ci_file_path}")
    
    async def _generate_github_actions(self, project_path: str, config: Dict[str, Any]):
        """生成GitHub Actions配置"""
        github_actions_content = {
            'name': 'CI/CD Pipeline',
            'on': {
                'push': {'branches': ['main', 'develop']},
                'pull_request': {'branches': ['main']}
            },
            'jobs': {
                'test': {
                    'runs-on': 'ubuntu-latest',
                    'steps': [
                        {'uses': 'actions/checkout@v3'},
                        {
                            'name': 'Set up Python',
                            'uses': 'actions/setup-python@v4',
                            'with': {'python-version': '3.9'}
                        },
                        {
                            'name': 'Install dependencies',
                            'run': 'pip install -r requirements.txt'
                        },
                        {
                            'name': 'Run tests',
                            'run': 'pytest --cov=. --cov-report=xml'
                        },
                        {
                            'name': 'Quality check',
                            'run': 'flake8 . && black --check .'
                        }
                    ]
                },
                'auto-fix': {
                    'runs-on': 'ubuntu-latest',
                    'if': 'failure()',
                    'needs': 'test',
                    'steps': [
                        {'uses': 'actions/checkout@v3'},
                        {
                            'name': 'Auto fix',
                            'run': 'python -m bot_agent.tools.ci_fix_trigger --project-id=${{ github.repository }} --commit=${{ github.sha }}'
                        }
                    ]
                }
            }
        }
        
        actions_dir = os.path.join(project_path, '.github', 'workflows')
        os.makedirs(actions_dir, exist_ok=True)
        
        workflow_file = os.path.join(actions_dir, 'ci.yml')
        with open(workflow_file, 'w', encoding='utf-8') as f:
            yaml.dump(github_actions_content, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"✅ 生成GitHub Actions配置: {workflow_file}")
    
    async def run_comprehensive_tests(self, 
                                    project_path: str,
                                    test_types: List[TestType] = None) -> List[TestResult]:
        """运行综合测试"""
        if test_types is None:
            test_types = [TestType.UNIT, TestType.INTEGRATION, TestType.SECURITY]
        
        results = []
        
        for test_type in test_types:
            try:
                result = await self._run_specific_test(project_path, test_type)
                results.append(result)
            except Exception as e:
                logger.error(f"运行{test_type.value}测试失败: {e}")
                results.append(TestResult(
                    test_type=test_type,
                    success=False,
                    duration=0,
                    coverage=None,
                    failed_tests=[],
                    output="",
                    error_message=str(e)
                ))
        
        return results
    
    async def _run_specific_test(self, project_path: str, test_type: TestType) -> TestResult:
        """运行特定类型的测试"""
        start_time = time.time()
        
        if test_type == TestType.UNIT:
            return await self._run_unit_tests(project_path, start_time)
        elif test_type == TestType.INTEGRATION:
            return await self._run_integration_tests(project_path, start_time)
        elif test_type == TestType.SECURITY:
            return await self._run_security_tests(project_path, start_time)
        elif test_type == TestType.PERFORMANCE:
            return await self._run_performance_tests(project_path, start_time)
        elif test_type == TestType.REGRESSION:
            return await self._run_regression_tests(project_path, start_time)
        else:
            raise ValueError(f"不支持的测试类型: {test_type}")
    
    async def _run_unit_tests(self, project_path: str, start_time: float) -> TestResult:
        """运行单元测试"""
        try:
            # 运行pytest
            process = await asyncio.create_subprocess_exec(
                'python', '-m', 'pytest', 'tests/', '--cov=.', '--cov-report=term-missing', '--tb=short',
                cwd=project_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            duration = time.time() - start_time
            
            output = stdout.decode('utf-8') + stderr.decode('utf-8')
            success = process.returncode == 0
            
            # 解析覆盖率
            coverage = self._parse_coverage(output)
            
            # 解析失败的测试
            failed_tests = self._parse_failed_tests(output)
            
            return TestResult(
                test_type=TestType.UNIT,
                success=success,
                duration=duration,
                coverage=coverage,
                failed_tests=failed_tests,
                output=output
            )
            
        except Exception as e:
            return TestResult(
                test_type=TestType.UNIT,
                success=False,
                duration=time.time() - start_time,
                coverage=None,
                failed_tests=[],
                output="",
                error_message=str(e)
            )
    
    async def _run_security_tests(self, project_path: str, start_time: float) -> TestResult:
        """运行安全测试"""
        try:
            # 运行bandit安全检查
            process = await asyncio.create_subprocess_exec(
                'bandit', '-r', '.', '-f', 'json',
                cwd=project_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            duration = time.time() - start_time
            
            output = stdout.decode('utf-8')
            success = process.returncode == 0
            
            # 解析安全问题
            failed_tests = []
            if not success and output:
                try:
                    import json
                    bandit_data = json.loads(output)
                    failed_tests = [
                        f"{result['filename']}:{result['line_number']} - {result['issue_text']}"
                        for result in bandit_data.get('results', [])
                    ]
                except json.JSONDecodeError:
                    pass
            
            return TestResult(
                test_type=TestType.SECURITY,
                success=success,
                duration=duration,
                coverage=None,
                failed_tests=failed_tests,
                output=output
            )
            
        except Exception as e:
            return TestResult(
                test_type=TestType.SECURITY,
                success=False,
                duration=time.time() - start_time,
                coverage=None,
                failed_tests=[],
                output="",
                error_message=str(e)
            )
    
    async def _run_integration_tests(self, project_path: str, start_time: float) -> TestResult:
        """运行集成测试"""
        # 这里可以实现集成测试逻辑
        return TestResult(
            test_type=TestType.INTEGRATION,
            success=True,
            duration=time.time() - start_time,
            coverage=None,
            failed_tests=[],
            output="集成测试通过"
        )
    
    async def _run_performance_tests(self, project_path: str, start_time: float) -> TestResult:
        """运行性能测试"""
        # 这里可以实现性能测试逻辑
        return TestResult(
            test_type=TestType.PERFORMANCE,
            success=True,
            duration=time.time() - start_time,
            coverage=None,
            failed_tests=[],
            output="性能测试通过"
        )
    
    async def _run_regression_tests(self, project_path: str, start_time: float) -> TestResult:
        """运行回归测试"""
        # 这里可以实现回归测试逻辑
        return TestResult(
            test_type=TestType.REGRESSION,
            success=True,
            duration=time.time() - start_time,
            coverage=None,
            failed_tests=[],
            output="回归测试通过"
        )
    
    def _parse_coverage(self, output: str) -> Optional[float]:
        """解析测试覆盖率"""
        try:
            import re
            # 查找覆盖率信息
            coverage_match = re.search(r'TOTAL.*?(\d+)%', output)
            if coverage_match:
                return float(coverage_match.group(1))
        except Exception:
            pass
        return None
    
    def _parse_failed_tests(self, output: str) -> List[str]:
        """解析失败的测试"""
        failed_tests = []
        try:
            import re
            # 查找失败的测试
            failed_matches = re.findall(r'FAILED (.*?) -', output)
            failed_tests.extend(failed_matches)
        except Exception:
            pass
        return failed_tests
    
    async def create_regression_test(self, 
                                   project_id: str,
                                   error_signature: str,
                                   test_case: Dict[str, Any]) -> str:
        """创建回归测试"""
        try:
            test_id = f"regression_{int(time.time() * 1000)}"
            
            if project_id not in self.regression_tests:
                self.regression_tests[project_id] = {}
            
            self.regression_tests[project_id][test_id] = {
                'error_signature': error_signature,
                'test_case': test_case,
                'created_at': time.time(),
                'last_run': None,
                'success_count': 0,
                'failure_count': 0
            }
            
            logger.info(f"✅ 创建回归测试: {test_id}")
            return test_id
            
        except Exception as e:
            logger.error(f"创建回归测试失败: {e}")
            raise
    
    async def validate_fix_with_tests(self, 
                                    project_path: str,
                                    fix_description: str) -> Dict[str, Any]:
        """使用测试验证修复"""
        try:
            logger.info("🧪 开始测试验证修复...")
            
            # 运行综合测试
            test_results = await self.run_comprehensive_tests(project_path)
            
            # 分析测试结果
            total_tests = len(test_results)
            passed_tests = sum(1 for result in test_results if result.success)
            
            validation_result = {
                'overall_success': passed_tests == total_tests,
                'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
                'test_results': test_results,
                'fix_description': fix_description,
                'validation_time': time.time()
            }
            
            logger.info(f"✅ 测试验证完成: {passed_tests}/{total_tests} 通过")
            return validation_result
            
        except Exception as e:
            logger.error(f"测试验证失败: {e}")
            return {
                'overall_success': False,
                'error': str(e),
                'validation_time': time.time()
            }


# 全局CI集成管理器实例
global_ci_integration_manager = CIIntegrationManager()
