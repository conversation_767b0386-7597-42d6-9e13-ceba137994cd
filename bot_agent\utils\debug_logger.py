#!/usr/bin/env python3
"""
调试日志收集器

专门收集和管理调试过程中的重点日志，支持按会话分组和实时查看
"""

import logging
import time
import threading
import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime
import json

class DebugLogLevel(Enum):
    """调试日志级别"""
    TRACE = "trace"      # 详细跟踪
    DEBUG = "debug"      # 调试信息
    INFO = "info"        # 一般信息
    WARNING = "warning"  # 警告
    ERROR = "error"      # 错误
    CRITICAL = "critical" # 严重错误

class DebugLogCategory(Enum):
    """调试日志分类"""
    ROUTER = "router"                    # 智能路由器
    COORDINATOR = "coordinator"          # 智能修复协调器
    AIDER = "aider"                     # Aider执行器
    COMMAND = "command"                 # 命令执行
    API = "api"                         # API调用
    FILE_OPERATION = "file_operation"   # 文件操作
    SYSTEM = "system"                   # 系统级别

@dataclass
class DebugLogEntry:
    """调试日志条目"""
    timestamp: float
    session_id: str
    category: DebugLogCategory
    level: DebugLogLevel
    component: str
    message: str
    details: Dict[str, Any] = None
    context: Dict[str, Any] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp,
            'session_id': self.session_id,
            'category': self.category.value,
            'level': self.level.value,
            'component': self.component,
            'message': self.message,
            'details': self.details or {},
            'context': self.context or {},
            'formatted_time': time.strftime('%H:%M:%S', time.localtime(self.timestamp))
        }

class DebugLogger:
    """调试日志收集器"""

    def __init__(self, max_entries: int = 10000, log_file: str = None):
        self.max_entries = max_entries
        self.entries: List[DebugLogEntry] = []
        self.session_entries: Dict[str, List[DebugLogEntry]] = {}
        self.lock = threading.Lock()

        # 设置日志文件路径
        if log_file is None:
            # 默认保存到项目根目录的logs/debug_logs.json
            project_root = Path(__file__).parent.parent.parent
            log_dir = project_root / "logs"
            log_dir.mkdir(exist_ok=True)
            self.log_file = log_dir / "debug_logs.json"
        else:
            self.log_file = Path(log_file)
            self.log_file.parent.mkdir(parents=True, exist_ok=True)

        # 设置标准日志处理器
        self.logger = logging.getLogger('debug_logger')
        self.logger.setLevel(logging.DEBUG)

        # 启动时加载已有的日志
        self._load_logs_from_file()

    def log(self, session_id: str, category: DebugLogCategory, level: DebugLogLevel,
            component: str, message: str, details: Dict[str, Any] = None,
            context: Dict[str, Any] = None):
        """记录调试日志"""
        entry = DebugLogEntry(
            timestamp=time.time(),
            session_id=session_id,
            category=category,
            level=level,
            component=component,
            message=message,
            details=details,
            context=context
        )

        with self.lock:
            # 添加到全局列表
            self.entries.append(entry)
            if len(self.entries) > self.max_entries:
                self.entries.pop(0)

            # 添加到会话列表
            if session_id not in self.session_entries:
                self.session_entries[session_id] = []
            self.session_entries[session_id].append(entry)

            # 限制每个会话的日志数量
            if len(self.session_entries[session_id]) > 1000:
                self.session_entries[session_id].pop(0)

        # 同时记录到标准日志
        log_level = getattr(logging, level.value.upper(), logging.INFO)
        self.logger.log(log_level, f"[{category.value}:{component}] {message}")

        # 保存到文件
        self._save_logs_to_file()

    def get_session_logs(self, session_id: str,
                        category: Optional[DebugLogCategory] = None,
                        level: Optional[DebugLogLevel] = None,
                        component: Optional[str] = None,
                        limit: int = 100) -> List[Dict[str, Any]]:
        """获取会话日志"""
        with self.lock:
            entries = self.session_entries.get(session_id, [])

            # 过滤条件
            if category:
                entries = [e for e in entries if e.category == category]
            if level:
                entries = [e for e in entries if e.level == level]
            if component:
                entries = [e for e in entries if component.lower() in e.component.lower()]

            # 返回最新的条目
            return [entry.to_dict() for entry in entries[-limit:]]

    def get_recent_logs(self, minutes: int = 10,
                       category: Optional[DebugLogCategory] = None,
                       level: Optional[DebugLogLevel] = None) -> List[Dict[str, Any]]:
        """获取最近的日志"""
        cutoff_time = time.time() - (minutes * 60)

        with self.lock:
            entries = [e for e in self.entries if e.timestamp >= cutoff_time]

            # 过滤条件
            if category:
                entries = [e for e in entries if e.category == category]
            if level:
                entries = [e for e in entries if e.level == level]

            return [entry.to_dict() for entry in entries]

    def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """获取会话日志摘要"""
        with self.lock:
            entries = self.session_entries.get(session_id, [])

            if not entries:
                return {
                    'session_id': session_id,
                    'total_logs': 0,
                    'categories': {},
                    'levels': {},
                    'components': {},
                    'time_range': None
                }

            # 统计信息
            categories = {}
            levels = {}
            components = {}

            for entry in entries:
                # 分类统计
                cat = entry.category.value
                categories[cat] = categories.get(cat, 0) + 1

                # 级别统计
                lvl = entry.level.value
                levels[lvl] = levels.get(lvl, 0) + 1

                # 组件统计
                comp = entry.component
                components[comp] = components.get(comp, 0) + 1

            return {
                'session_id': session_id,
                'total_logs': len(entries),
                'categories': categories,
                'levels': levels,
                'components': components,
                'time_range': {
                    'start': entries[0].timestamp,
                    'end': entries[-1].timestamp,
                    'duration': entries[-1].timestamp - entries[0].timestamp
                }
            }

    def clear_session_logs(self, session_id: str):
        """清空会话日志"""
        with self.lock:
            if session_id in self.session_entries:
                del self.session_entries[session_id]

    def clear_all_logs(self):
        """清空所有日志"""
        with self.lock:
            self.entries.clear()
            self.session_entries.clear()

    def get_active_sessions(self, minutes: int = 60) -> List[str]:
        """获取活跃会话列表"""
        cutoff_time = time.time() - (minutes * 60)

        with self.lock:
            active_sessions = set()
            for entry in self.entries:
                if entry.timestamp >= cutoff_time:
                    active_sessions.add(entry.session_id)

            return list(active_sessions)

    def _load_logs_from_file(self):
        """从文件加载日志"""
        try:
            if self.log_file.exists():
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 重建日志条目
                for entry_data in data.get('entries', []):
                    try:
                        entry = DebugLogEntry(
                            timestamp=entry_data['timestamp'],
                            session_id=entry_data['session_id'],
                            category=DebugLogCategory(entry_data['category']),
                            level=DebugLogLevel(entry_data['level']),
                            component=entry_data['component'],
                            message=entry_data['message'],
                            details=entry_data.get('details'),
                            context=entry_data.get('context')
                        )

                        self.entries.append(entry)

                        # 添加到会话分组
                        session_id = entry.session_id
                        if session_id not in self.session_entries:
                            self.session_entries[session_id] = []
                        self.session_entries[session_id].append(entry)

                    except Exception as e:
                        print(f"⚠️ 跳过无效的日志条目: {e}")
                        continue

                print(f"✅ 从文件加载了 {len(self.entries)} 条调试日志")
        except Exception as e:
            print(f"⚠️ 加载调试日志文件失败: {e}")

    def _save_logs_to_file(self):
        """保存日志到文件"""
        try:
            # 只保存最近的日志（避免文件过大）
            recent_entries = self.entries[-5000:] if len(self.entries) > 5000 else self.entries

            data = {
                'last_updated': datetime.now().isoformat(),
                'total_entries': len(recent_entries),
                'entries': []
            }

            for entry in recent_entries:
                entry_data = {
                    'timestamp': entry.timestamp,
                    'session_id': entry.session_id,
                    'category': entry.category.value,
                    'level': entry.level.value,
                    'component': entry.component,
                    'message': entry.message,
                    'details': entry.details,
                    'context': entry.context
                }
                data['entries'].append(entry_data)

            # 原子写入（先写临时文件，再重命名）
            temp_file = self.log_file.with_suffix('.tmp')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 重命名为正式文件
            temp_file.replace(self.log_file)

        except Exception as e:
            print(f"⚠️ 保存调试日志文件失败: {e}")

# 全局调试日志收集器
global_debug_logger = DebugLogger()

# 便捷函数
def debug_log(session_id: str, category: DebugLogCategory, level: DebugLogLevel,
              component: str, message: str, **kwargs):
    """便捷的调试日志记录函数"""
    global_debug_logger.log(session_id, category, level, component, message,
                           details=kwargs.get('details'), context=kwargs.get('context'))

# 特定组件的便捷函数
def log_router(session_id: str, level: DebugLogLevel, message: str, **kwargs):
    """路由器日志"""
    debug_log(session_id, DebugLogCategory.ROUTER, level, "IntelligentFixRouter", message, **kwargs)

def log_coordinator(session_id: str, level: DebugLogLevel, message: str, **kwargs):
    """协调器日志"""
    debug_log(session_id, DebugLogCategory.COORDINATOR, level, "IntelligentFixCoordinator", message, **kwargs)

def log_aider(session_id: str, level: DebugLogLevel, message: str, **kwargs):
    """Aider日志"""
    debug_log(session_id, DebugLogCategory.AIDER, level, "AiderExecutor", message, **kwargs)

def log_command(session_id: str, level: DebugLogLevel, message: str, **kwargs):
    """命令执行日志"""
    debug_log(session_id, DebugLogCategory.COMMAND, level, "CommandExecutor", message, **kwargs)
