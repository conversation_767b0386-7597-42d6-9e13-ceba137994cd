2025-05-31 11:49:14,782 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-31 11:49:14,783 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-31 11:49:14,783 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-31 11:49:14,784 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-31 11:49:14,784 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-31 11:49:14,791 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-31 11:49:14,792 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-31 11:49:15,319 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-31 11:49:15,319 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-31 11:49:15,319 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:136 - _process_with_aider - 使用AiderBasedTaskExecutor处理任务: 作业失败分析 - lint (Job 1032)
2025-05-31 11:49:15,320 - bot_agent.engines.simple_progress_tracker - WARNING - simple_progress_tracker.py:50 - send_progress_update - 缺少项目ID或Issue IID，跳过进度更新
2025-05-31 11:49:15,320 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-31 11:49:15,321 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-31 11:49:15,321 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-31 11:49:16,237 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-31 11:49:16,238 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:99 - __init__ - 使用环境变量中的项目下载目录: E:\aider-git-repos\
2025-05-31 11:49:16,238 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\aider-git-repos\
2025-05-31 11:49:16,239 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-31 11:49:16,239 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:132 - setup_git_config - 使用环境变量中的Git用户名: aider-worker
2025-05-31 11:49:16,239 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:136 - setup_git_config - 使用环境变量中的Git用户邮箱: <EMAIL>
2025-05-31 11:49:16,240 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:143 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_NAME=aider-worker, GIT_COMMITTER_NAME=aider-worker
2025-05-31 11:49:16,240 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:148 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_EMAIL=<EMAIL>, GIT_COMMITTER_EMAIL=<EMAIL>
2025-05-31 11:49:16,241 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: information_query
2025-05-31 11:49:16,242 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TerminalTools
2025-05-31 11:49:16,242 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TestingTools
2025-05-31 11:49:16,242 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: LogAnalysisTools
2025-05-31 11:49:16,242 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DatabaseTools
2025-05-31 11:49:16,243 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DependencyTools
2025-05-31 11:49:16,243 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DebugTools
2025-05-31 11:49:16,243 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: FrontendDebugTools
2025-05-31 11:49:16,244 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: RefactorTools
2025-05-31 11:49:16,244 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DocumentationTools
2025-05-31 11:49:16,244 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: SecurityTools
2025-05-31 11:49:16,244 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: CodeGenerationTools
2025-05-31 11:49:16,244 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-31 11:49:16,246 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-31 11:49:16,246 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-31 11:49:16,247 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-31 11:49:16,247 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-31 11:49:16,247 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-31 11:49:16,248 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:174 - _process_with_aider - 使用Aider执行引擎处理 TaskType.BUG_FIX 类型任务
2025-05-31 11:49:16,248 - bot_agent.engines.task_executor - INFO - task_executor.py:64 - execute_task - 开始使用Aider执行任务 184c0021-2c5f-472b-904b-8f5000d22eb0
2025-05-31 11:49:16,258 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-31 11:49:16,259 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-31 11:49:16,259 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-31 11:49:16,260 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-31 11:49:17,572 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-31 11:49:17,572 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"id":9,"description":null,"name":"ai-proxy","name_with_namespace":"Longer / ai-proxy","path":"ai-proxy","path_with_namespace":"Longer/ai-proxy","created_at":"2025-05-26T12:38:44.596Z","default_branch":"main","tag_list":[],"topics":[],"ssh_url_to_repo":"git@***************:Longer/ai-proxy.git","http_url_to_repo":"http://***************/Longer/ai-proxy.git","web_url":"http://***************/Longer/ai-proxy","readme_url":"http://***************/Longer/ai-proxy/-/blob/main/README.md","forks_count":'
2025-05-31 11:49:17,572 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-31T03:24:51.829Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-31T03:41:37.145Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-31 11:49:17,574 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-31 11:49:17,574 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-31 11:49:17,575 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-31 11:49:17,579 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-31 11:49:17,580 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 1.33s
2025-05-31 11:49:18,088 - bot_agent.memory.project_memory - INFO - project_memory.py:42 - __init__ - ProjectMemoryManager initialized for project: E:\aider-git-repos\ai-proxy
2025-05-31 11:49:18,098 - bot_agent.memory.global_memory - INFO - global_memory.py:60 - load_memory - Global memory loaded
2025-05-31 11:49:18,098 - bot_agent.memory.memory_integration - INFO - memory_integration.py:78 - prepare_context_for_task - Prepared context for task: 927 characters
2025-05-31 11:49:18,099 - bot_agent.engines.tool_integration - INFO - tool_integration.py:49 - enhance_aider_request - 开始增强Aider请求
2025-05-31 11:49:18,099 - bot_agent.tools.tool_router - INFO - tool_router.py:184 - analyze_task - 分析任务: 
任务类型: TaskType.BUG_FIX
任务标题: 作业失败分析 - lint (Job 1032)

任务描述:

## 🚨 作业失败自动分析

**项目**: ai-proxy
**作业名称**: lint
**作业ID**: 1032
**Pipeline ID**: 276
**阶段**: test
**分支**: aider-plus-dev
**状态**: failed
**失败原因**: script_failure

### 任务要求
1. 分析作业失败的具体原因
2. 收集作业日志和错误信息
3. 提供针对性的修复建议
4. 如果是部署作业，评估回滚需求

### 自动化指令
请立即分析Job 1032的失败原因，收集详细日志，并提供修复方案。


# 全局工作记忆
## 相关工作习惯
- task_preference: 成功处理 TaskType.BUG_FIX 类型任务 (频率: 125)
- task_preference: 成功处理 TaskType.PROJECT_ANALYSIS 类型任务 (频率: 4)
- task_preference: 成功处理 OPTIMIZATION 类型任务 (频率: 1)

## 偏好设置
tools: VSCode, PowerShell, GitLab, 2025-05-27T15:23:37.010003, GitLab, aider-plus; programming_languages: Python, JavaScript, Rust, 2025-05-31T11:41:34.631180, go, bug_fix, 1; frameworks: FastAPI, PostgreSQL, Redis, pytest, 2025-05-31T10:14:03.869694, fastapi, 作业失败分析 - test (Job 1012), 1


# 项目记忆
## 项目架构
Overview: AI API代理服务; Patterns: 代理模式, 策略模式, 工厂模式; Technologies: FastAPI, asyncio, httpx

## general 编码规范
- 作业失败分析 - lint (Job 653)
- 作业失败分析 - lint (Job 664)
- 作业失败分析 - lint (Job 677)



# 环境信息
## 环境配置
- last_project_path: E:\aider-git-repos\ai-proxy
- last_task_type: bug_fix
- os: Windows 11
- python_version: 3.9
- conda_env: aider-plus
- git_user: aider-worker
- workspace: E:\Projects\aider-plus
- last_used_tool: aider


## 🚨 严格执行指令 - 禁止偏离

### ⚠️ 绝对禁止的行为：
1. **禁止创建任何新文件** - 不允许创建.py、.js、.md等任何新文件
2. **禁止编写代码** - 不允许编写类、函数、测试代码
3. **禁止基于假设分析** - 必须基于真实数据和日志
4. **禁止提供通用建议** - 必须针对具体问题提供具体解决方案
5. **禁止代码审查** - 当前任务不是代码审查，是问题分析

### ✅ 必须执行的步骤（严格按顺序）：

#### 第1步：获取真实数据（必须完成）
- 使用GitLabClient获取Job的实际日志内容
- 如果无法获取，明确说明原因并停止
- 不允许基于"没有日志"进行假设性分析

#### 第2步：分析具体问题（基于真实数据）
- 使用LogAnalysisTools分析实际的错误日志
- 识别具体的错误类型、文件、行号
- 确定失败的根本原因

#### 第3步：执行具体修复（针对性解决）
- 使用TerminalTools执行针对性的修复命令
- 修复具体识别出的问题
- 不执行通用的格式化命令

#### 第4步：验证修复效果
- 使用TestingTools验证修复是否成功
- 确认问题已解决

### 🎯 当前任务要求：
如果这是作业失败分析任务，你必须：
1. 获取指定Job ID的实际失败日志
2. 分析日志中的具体错误信息
3. 提供针对这些具体错误的修复方案
4. 验证修复效果

### 🚫 严格禁止：
- 说"Since we don't have the actual log files"
- 提供black、flake8等通用命令
- 创建LintAnalyzer等新类
- 进行代码审查
- 创建测试文件

现在开始执行，严格遵循上述要求：

2025-05-31 11:49:18,103 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 4, 模式匹配: 0, 最终置信度: 0.54
2025-05-31 11:49:18,104 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 3, 模式匹配: 1, 最终置信度: 0.5199999999999999
2025-05-31 11:49:18,105 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 5, 模式匹配: 1, 最终置信度: 0.6299999999999999
2025-05-31 11:49:18,106 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 5, 模式匹配: 1, 最终置信度: 0.8099999999999999
2025-05-31 11:49:18,107 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 2, 模式匹配: 0, 最终置信度: 0.24
2025-05-31 11:49:18,107 - bot_agent.tools.tool_router - DEBUG - tool_router.py:252 - _calculate_confidence - 工具匹配分析 - 关键词匹配: 1, 模式匹配: 0, 最终置信度: 0.105
2025-05-31 11:49:18,107 - bot_agent.tools.tool_router - INFO - tool_router.py:217 - analyze_task - 生成了 6 个工具建议
2025-05-31 11:49:18,108 - bot_agent.engines.tool_integration - INFO - tool_integration.py:67 - enhance_aider_request - 请求增强完成，添加了 6 个工具建议
2025-05-31 11:49:18,108 - bot_agent.config.model_config - DEBUG - model_config.py:56 - get_code_generation_model - 获取代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-31 11:49:18,108 - bot_agent.engines.task_executor - INFO - task_executor.py:252 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-31 11:49:26,171 - bot_agent.engines.task_executor - INFO - task_executor.py:404 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-31 11:49:26,228 - bot_agent.engines.task_executor - INFO - task_executor.py:425 - _smart_file_discovery - 智能发现了 29 个相关文件: ['setup.py', 'api_proxy\\__init__.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_boundary.py', 'api_proxy\\config.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_analysis_error.py', 'tests\\test_lint_analysis_boundary.py', 'tests\\test_health_check.py', 'tests\\test_provider_security.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_provider_initialization.py', 'api_proxy\\models.py', 'tests\\test_job_failure_analysis_error.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'requirements.txt', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'tests\\test_sensitive_data.py', 'tests\\test_proxy_service_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_unit.py']
2025-05-31 11:49:26,231 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 🔍 启用Aider操作监控系统
2025-05-31 11:49:27,638 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:55 - log_operation - [AIDER_MONITOR] ✅ coder_created: 创建监控的Coder
2025-05-31 11:49:27,638 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   kwargs: {'main_model': Model(name='openrouter/deepseek/deepseek-chat-v3-0324:free', edit_format='diff', weak_model_name='openrouter/deepseek/deepseek-chat-v3-0324:free', use_repo_map=True, send_undo_reply=False, lazy=False, overeager=False, reminder='user', examples_as_sys_msg=True, extra_params=None, cache_control=False, caches_by_default=True, use_system_prompt=True, use_temperature=0.3, streaming=True, editor_model_name='openrouter/deepseek/deepseek-r1:free', editor_edit_format='editor-diff', reasoning_tag=None, remove_reasoning=None, system_prompt_prefix=None, accepts_settings=[]), 'io': <bot_agent.aider_extensions.aider_monitor.MonitoredIO object at 0x000001D93FA44770>, 'repo': <aider.repo.GitRepo object at 0x000001D93F8BF3E0>, 'fnames': ['setup.py', 'api_proxy\\__init__.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_boundary.py', 'api_proxy\\config.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_analysis_error.py', 'tests\\test_lint_analysis_boundary.py', 'tests\\test_health_check.py', 'tests\\test_provider_security.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_provider_initialization.py', 'api_proxy\\models.py', 'tests\\test_job_failure_analysis_error.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'requirements.txt', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'tests\\test_sensitive_data.py', 'tests\\test_proxy_service_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_unit.py'], 'use_git': False, 'auto_commits': False, 'dirty_commits': False, 'auto_lint': False, 'auto_test': False, 'stream': False, 'verbose': False, 'chat_language': 'Chinese', 'suggest_shell_commands': False, 'auto_accept_architect': True, 'map_tokens': 0, 'cache_prompts': False, 'num_cache_warming_pings': 0}
2025-05-31 11:49:27,640 - bot_agent.engines.task_executor - INFO - task_executor.py:302 - _execute_with_aider - 使用优化的Aider执行任务: 作业失败分析 - lint (Job 1032)
2025-05-31 11:49:27,642 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-31 11:49:27,642 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-31 11:49:27,643 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-31 11:49:27,643 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-31 11:49:31,303 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-31 11:49:31,303 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"id":9,"description":null,"name":"ai-proxy","name_with_namespace":"Longer / ai-proxy","path":"ai-proxy","path_with_namespace":"Longer/ai-proxy","created_at":"2025-05-26T12:38:44.596Z","default_branch":"main","tag_list":[],"topics":[],"ssh_url_to_repo":"git@***************:Longer/ai-proxy.git","http_url_to_repo":"http://***************/Longer/ai-proxy.git","web_url":"http://***************/Longer/ai-proxy","readme_url":"http://***************/Longer/ai-proxy/-/blob/main/README.md","forks_count":'
2025-05-31 11:49:31,304 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-31T03:24:51.829Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-31T03:41:37.145Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-31 11:49:31,306 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-31 11:49:31,306 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-31 11:49:31,307 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-31 11:49:31,308 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-31 11:49:31,308 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 3.67s
2025-05-31 11:49:31,308 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:136 - start_session - Started conversation session: task_1748663371
2025-05-31 11:49:31,308 - bot_agent.engines.task_executor - INFO - task_executor.py:773 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-31 11:49:31,309 - bot_agent.engines.task_executor - INFO - task_executor.py:777 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-31 11:49:31,312 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: job_failure_analysis
2025-05-31 11:49:31,312 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始作业失败分析
2025-05-31 11:49:31,313 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 提取Job ID
2025-05-31 11:49:31,314 - bot_agent.engines.task_executor - INFO - task_executor.py:1063 - _handle_job_failure_analysis - 提取到Job ID: 1032
2025-05-31 11:49:31,314 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 成功提取Job ID: 1032
2025-05-31 11:49:31,314 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取项目ID
2025-05-31 11:49:31,315 - bot_agent.engines.task_executor - INFO - task_executor.py:1071 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-31 11:49:31,315 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取到项目ID: 9
2025-05-31 11:49:31,316 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 准备工作空间
2025-05-31 11:49:31,316 - bot_agent.engines.task_executor - INFO - task_executor.py:1086 - _handle_job_failure_analysis - 作业失败分析使用项目路径: E:\aider-git-repos\ai-proxy
2025-05-31 11:49:31,317 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-31 11:49:31,317 - bot_agent.engines.task_executor - INFO - task_executor.py:1092 - _handle_job_failure_analysis - 已更新会话 task_1748663371 的项目路径为: E:\aider-git-repos\ai-proxy
2025-05-31 11:49:31,317 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 会话路径已更新
2025-05-31 11:49:31,317 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始智能分析
2025-05-31 11:49:31,318 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 预执行数据收集
2025-05-31 11:49:31,321 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-31 11:49:31,321 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-31 11:49:32,212 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-31 11:49:32,213 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-31 11:49:32,314 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:70 - get_job_info_and_log - 🔍 获取Job 1032的信息和日志...
2025-05-31 11:49:32,315 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:437 - get_job - Getting job 1032 in project 9
2025-05-31 11:49:32,315 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9/jobs/1032
2025-05-31 11:49:33,856 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-31 11:49:33,856 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"id":1032,"status":"failed","stage":"test","name":"lint","ref":"aider-plus-dev","tag":false,"coverage":null,"allow_failure":false,"created_at":"2025-05-31T03:47:59.046Z","started_at":"2025-05-31T03:48:05.415Z","finished_at":"2025-05-31T03:48:54.460Z","erased_at":null,"duration":49.044697,"queued_duration":5.131708,"user":{"id":3,"username":"Longer","name":"Longer","state":"active","locked":false,"avatar_url":"https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249'
2025-05-31 11:49:33,857 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 1032, 'status': 'failed', 'stage': 'test', 'name': 'lint', 'ref': 'aider-plus-dev', 'tag': False, 'coverage': None, 'allow_failure': False, 'created_at': '2025-05-31T03:47:59.046Z', 'started_at': '2025-05-31T03:48:05.415Z', 'finished_at': '2025-05-31T03:48:54.460Z', 'erased_at': None, 'duration': 49.044697, 'queued_duration': 5.131708, 'user': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer', 'created_at': '2025-04-27T02:44:28.626Z', 'bio': '', 'location': '', 'public_email': None, 'skype': '', 'linkedin': '', 'twitter': '', 'discord': '', 'website_url': '', 'organization': '', 'job_title': '', 'pronouns': None, 'bot': False, 'work_information': None, 'followers': 0, 'following': 0, 'local_time': None}, 'commit': {'id': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'short_id': 'c3b89fa1', 'created_at': '2025-05-31T11:41:26.000+08:00', 'parent_ids': ['6b60ca19687ec9f0c3e701314fb745fbdbcde5ed'], 'title': 'AI自动修改: 作业失败分析 - lint (Job 1028)', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1028)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'authored_date': '2025-05-31T11:41:26.000+08:00', 'committer_name': 'Longer (aider)', 'committer_email': '<EMAIL>', 'committed_date': '2025-05-31T11:41:26.000+08:00', 'trailers': {}, 'extended_trailers': {}, 'web_url': 'http://***************/Longer/ai-proxy/-/commit/c3b89fa142b5d5cf1bc144dbed6ba3d35405170c'}, 'pipeline': {'id': 276, 'iid': 98, 'project_id': 9, 'sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'ref': 'aider-plus-dev', 'status': 'failed', 'source': 'push', 'created_at': '2025-05-31T03:41:37.808Z', 'updated_at': '2025-05-31T03:48:55.658Z', 'web_url': 'http://***************/Longer/ai-proxy/-/pipelines/276'}, 'failure_reason': 'script_failure', 'web_url': 'http://***************/Longer/ai-proxy/-/jobs/1032', 'project': {'ci_job_token_scope_enabled': False}, 'artifacts': [], 'runner': {'id': 1, 'description': 'docker-runner-137', 'ip_address': None, 'active': True, 'paused': False, 'is_shared': True, 'runner_type': 'instance_type', 'name': None, 'online': True, 'status': 'online'}, 'runner_manager': {'id': 2, 'system_id': 'r_zhwE8WyyfNZL', 'version': '17.11.0', 'revision': '0f67ff19', 'platform': 'linux', 'architecture': 'amd64', 'created_at': '2025-04-27T09:00:17.697Z', 'contacted_at': '2025-05-31T03:48:55.808Z', 'ip_address': '***************', 'status': 'online'}, 'artifacts_expire_at': None, 'archived': False, 'tag_list': []}
2025-05-31 11:49:33,858 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:444 - get_job - 成功获取作业信息: Job 1032 - lint (failed)
2025-05-31 11:49:33,858 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:466 - get_job_log - Getting log for job 1032 in project 9
2025-05-31 11:49:34,772 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:484 - get_job_log - 成功获取作业日志: Job 1032, 长度: 6607 字符
2025-05-31 11:49:34,772 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:487 - get_job_log - 日志前5行: ['\x1b[0KRunning with gitlab-runner 17.11.0 (0f67ff19)\x1b[0;m', '\x1b[0K  on docker-runner-137 dDoxCnU6M, system ID: r_zhwE8WyyfNZL\x1b[0;m', 'section_start:1748663286:prepare_executor\r\x1b[0K\x1b[0K\x1b[36;1mPreparing the "docker" executor\x1b[0;m\x1b[0;m', '\x1b[0KUsing Docker executor with image python:3.9-slim ...\x1b[0;m', '\x1b[0KUsing locally found image version due to "if-not-present" pull policy\x1b[0;m']
2025-05-31 11:49:34,773 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-31 11:49:34,773 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:535 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-31 11:49:34,775 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:262 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpsrr04997.log']
2025-05-31 11:49:34,796 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:550 - analyze_job_log - ✅ 作业日志分析完成
2025-05-31 11:49:34,797 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 调用AI进行智能分析
2025-05-31 11:49:34,798 - bot_agent.engines.task_executor - INFO - task_executor.py:1198 - _handle_job_failure_analysis - 🧠 开始使用Bot推理分析器进行作业失败分析...
2025-05-31 11:49:34,801 - bot_agent.config.model_config - DEBUG - model_config.py:44 - get_analysis_model - 获取推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-31 11:49:34,801 - bot_agent.config.model_config - DEBUG - model_config.py:84 - convert_aider_to_openrouter_format - 转换模型名称: openrouter/deepseek/deepseek-r1:free -> deepseek/deepseek-r1:free
2025-05-31 11:49:34,801 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:44 - __init__ - BotReasoningAnalyzer initialized with model: deepseek/deepseek-r1:free
2025-05-31 11:49:34,802 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:61 - analyze_job_failure - 🧠 开始Bot级别推理分析...
2025-05-31 11:49:35,426 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:194 - _call_reasoning_model - 🧠 调用推理模型: deepseek/deepseek-r1:free
2025-05-31 11:50:13,541 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:214 - _call_reasoning_model - 🧠 推理模型响应长度: 819 字符
2025-05-31 11:50:13,544 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 1 for session task_1748663371: 第1轮：Bot推理分析 - 真实AI调用
2025-05-31 11:50:13,544 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:89 - analyze_job_failure - ✅ 已记录真实AI调用到会话日志，时长: 38.74秒
2025-05-31 11:50:13,544 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - Bot推理分析完成，置信度: 0.95
2025-05-31 11:50:13,546 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 2 for session task_1748663371: 第2轮：智能分析结果总结
2025-05-31 11:50:13,546 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 启动智能修复协调器
2025-05-31 11:50:13,547 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 执行智能修复路由分析
2025-05-31 11:50:13,571 - bot_agent.executors.aider_executor - INFO - aider_executor.py:58 - _check_aider_availability - Aider模块可用
2025-05-31 11:50:13,572 - bot_agent.executors.aider_executor - INFO - aider_executor.py:45 - __init__ - AiderExecutor initialized
2025-05-31 11:50:13,574 - bot_agent.executors.aider_executor - INFO - aider_executor.py:58 - _check_aider_availability - Aider模块可用
2025-05-31 11:50:13,575 - bot_agent.executors.aider_executor - INFO - aider_executor.py:45 - __init__ - AiderExecutor initialized
2025-05-31 11:50:13,575 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:32 - __init__ - IntelligentFixCoordinator initialized with learning capabilities
2025-05-31 11:50:13,575 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:51 - execute_intelligent_fix - 🚀 启动智能修复协调器...
2025-05-31 11:50:13,727 - bot_agent.tools.intelligent_fix_coordinator - WARNING - intelligent_fix_coordinator.py:396 - _collect_git_info - 收集Git信息失败: 'NoneType' object has no attribute 'strip'
2025-05-31 11:50:13,728 - bot_agent.tools.performance_monitor - INFO - performance_monitor.py:223 - cached_operation - 💾 缓存存储: gather_project_context
2025-05-31 11:50:13,728 - bot_agent.tools.performance_monitor - INFO - performance_monitor.py:258 - controlled_ai_call - 🤖 开始AI调用: intelligent_fix_routing
2025-05-31 11:50:13,728 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:103 - analyze_and_route - 🧠 开始智能修复路由分析...
2025-05-31 11:50:13,733 - bot_agent.tools.error_classifier - INFO - error_classifier.py:91 - classify_errors - ✅ 成功分类 3 个错误
2025-05-31 11:50:13,733 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:122 - analyze_and_route - 📊 错误分类完成: 3 个错误
2025-05-31 11:50:13,734 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:293 - _deep_reasoning_analysis - 🚀 启动增强LLM调用...
2025-05-31 11:50:13,734 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:257 - call_reasoning_ai - 🔥 开始调用DeepSeek R1推理模型进行智能路由分析...
2025-05-31 11:50:14,348 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:269 - call_reasoning_ai - 📤 发送请求到OpenRouter API: deepseek/deepseek-r1:free
2025-05-31 11:50:14,355 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-31 11:50:14,355 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-31 11:50:14,356 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-31 11:50:14,356 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '23da01f4-**************-0ade8d71b199', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': 'f3fd7b42-e951-4a60-8558-5debe019f4b7', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'f63cb523-419c-4438-831c-4c6e2fa3804f', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3469'}
2025-05-31 11:50:14,357 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-31 11:50:14,357 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-31 11:50:14,357 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-31 11:50:14,358 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-31 11:50:14,358 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '23da01f4-**************-0ade8d71b199', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': 'f3fd7b42-e951-4a60-8558-5debe019f4b7', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'f63cb523-419c-4438-831c-4c6e2fa3804f', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3469'}
2025-05-31 11:50:14,360 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 276, 'iid': 98, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'before_sha': '6b60ca19687ec9f0c3e701314fb745fbdbcde5ed', 'source': 'push', 'status': 'failed', 'detailed_status': 'failed', 'stages': ['test', 'build'], 'created_at': '2025-05-31 03:41:37 UTC', 'finished_at': '2025-05-31 03:48:55 UTC', 'duration': 162, 'queued_duration': 7, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/276'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1028)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 1028)', 'timestamp': '2025-05-31T11:41:26+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 1032, 'stage': 'test', 'name': 'lint', 'status': 'failed', 'created_at': '2025-05-31 03:47:59 UTC', 'started_at': '2025-05-31 03:48:05 UTC', 'finished_at': '2025-05-31 03:48:54 UTC', 'duration': 49.044697, 'queued_duration': 5.131708, 'failure_reason': 'script_failure', 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 1031, 'stage': 'build', 'name': 'build', 'status': 'skipped', 'created_at': '2025-05-31 03:41:37 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 1029, 'stage': 'test', 'name': 'test', 'status': 'failed', 'created_at': '2025-05-31 03:41:37 UTC', 'started_at': '2025-05-31 03:41:43 UTC', 'finished_at': '2025-05-31 03:43:37 UTC', 'duration': 113.199156, 'queued_duration': 3.569286, 'failure_reason': 'script_failure', 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-31 11:50:14,361 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-31 11:50:14,361 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-31 11:50:14,362 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 276 for aider-plus-dev is failed (Project: ai-proxy, User: Longer)
2025-05-31 11:50:14,362 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 276 status failed recorded (no AI monitoring needed)
2025-05-31 11:50:14,363 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 276 status failed recorded'}
2025-05-31 11:50:41,338 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:280 - call_reasoning_ai - 📥 收到API响应: status=200
2025-05-31 11:50:41,338 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:285 - call_reasoning_ai - ✅ DeepSeek R1推理完成，响应长度: 861 字符
2025-05-31 11:50:41,341 - bot_agent.tools.intelligent_fix_router - WARNING - intelligent_fix_router.py:314 - _deep_reasoning_analysis - 推理模型响应JSON解析失败，使用文本分析
2025-05-31 11:50:41,342 - bot_agent.tools.intelligent_fix_router - WARNING - intelligent_fix_router.py:324 - _deep_reasoning_analysis - JSON解析失败但有AI响应，使用文本分析，耗时: 27.61秒
2025-05-31 11:50:41,342 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:468 - _generate_automated_commands - 🔧 开始生成自动化命令...
2025-05-31 11:50:41,343 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:469 - _generate_automated_commands -    - 错误数量: 3
2025-05-31 11:50:41,343 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:470 - _generate_automated_commands -    - 操作系统: Windows
2025-05-31 11:50:41,344 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:471 - _generate_automated_commands -    - 错误列表: [{'type': 'flake8_config_error', 'severity': 'medium', 'category': 'lint', 'line_number': 92, 'file_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpsrr04997.log', 'content': "ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'", 'solutions': ['检查.flake8配置文件', '修复extend-ignore选项格式', '确保错误代码格式正确(如E203,W503)', '移除无效的错误代码'], 'timestamp': None}, {'type': 'generic_job_failure', 'severity': 'medium', 'category': 'job', 'line_number': 97, 'file_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpsrr04997.log', 'content': '\x1b[0K\x1b[31;1mERROR: Job failed: exit code 1', 'solutions': ['查看详细错误日志', '检查作业配置', '验证环境设置', '重新运行作业'], 'timestamp': None}, 'Traceback (most recent call last):']
2025-05-31 11:50:41,346 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:483 - _generate_automated_commands -    - 处理错误 1: {'type': 'flake8_config_error', 'severity': 'medium', 'category': 'lint', 'line_number': 92, 'file_p...
2025-05-31 11:50:41,347 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:487 - _generate_automated_commands -      ✅ 检测到flake8错误
2025-05-31 11:50:41,347 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:489 - _generate_automated_commands -      ✅ 检测到extend-ignore配置错误
2025-05-31 11:50:41,348 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:494 - _generate_automated_commands -      📝 添加Windows命令: powershell -Command "(Get-Content .flake8 -ErrorAction SilentlyContinue) -replace '#.*', '' | Where-Object {$_.trim() -ne ''} | Set-Content .flake8"
2025-05-31 11:50:41,349 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:483 - _generate_automated_commands -    - 处理错误 2: {'type': 'generic_job_failure', 'severity': 'medium', 'category': 'job', 'line_number': 97, 'file_pa...
2025-05-31 11:50:41,349 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:521 - _generate_automated_commands -      ❌ 未识别的错误类型: {'type': 'generic_job_failure', 'severity': 'mediu...
2025-05-31 11:50:41,349 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:483 - _generate_automated_commands -    - 处理错误 3: Traceback (most recent call last):...
2025-05-31 11:50:41,350 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:521 - _generate_automated_commands -      ❌ 未识别的错误类型: Traceback (most recent call last):...
2025-05-31 11:50:41,351 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:523 - _generate_automated_commands - 🔍 第一轮命令生成完成，当前命令数量: 1
2025-05-31 11:50:41,351 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:546 - _generate_automated_commands - ✅ 自动化命令生成完成，总计 1 个命令:
2025-05-31 11:50:41,352 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:548 - _generate_automated_commands -    1. powershell -Command "(Get-Content .flake8 -ErrorAction SilentlyContinue) -replace '#.*', '' | Where-Object {$_.trim() -ne ''} | Set-Content .flake8"
2025-05-31 11:50:41,352 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:153 - analyze_and_route - 🎯 修复决策: automated (复杂度: simple, 置信度: 0.60)
2025-05-31 11:50:41,354 - bot_agent.tools.performance_monitor - INFO - performance_monitor.py:266 - controlled_ai_call - ✅ AI调用完成: intelligent_fix_routing
2025-05-31 11:50:41,359 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 3 for session task_1748663371: 第3轮：智能修复路由决策
2025-05-31 11:50:41,360 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:452 - _execute_automated_fix - 🤖 执行自动化修复策略...
2025-05-31 11:50:41,361 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:457 - _execute_automated_fix - 🚀 开始执行自动化修复...
2025-05-31 11:50:41,361 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:458 - _execute_automated_fix -    - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-31 11:50:41,362 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:459 - _execute_automated_fix -    - 会话ID: task_1748663371
2025-05-31 11:50:41,362 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:460 - _execute_automated_fix -    - 命令数量: 1
2025-05-31 11:50:41,363 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:461 - _execute_automated_fix -    - 修复策略: FixStrategy.AUTOMATED
2025-05-31 11:50:41,363 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:462 - _execute_automated_fix -    - 复杂度: FixComplexity.SIMPLE
2025-05-31 11:50:41,364 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:465 - _execute_automated_fix -    - 待执行命令:
2025-05-31 11:50:41,364 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:467 - _execute_automated_fix -      1. powershell -Command "(Get-Content .flake8 -ErrorAction SilentlyContinue) -replace '#.*', '' | Where-Object {$_.trim() -ne ''} | Set-Content .flake8"
2025-05-31 11:50:41,365 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:505 - _execute_automated_fix - 🔧 执行命令 1/1: powershell -Command "(Get-Content .flake8 -ErrorAction SilentlyContinue) -replace '#.*', '' | Where-Object {$_.trim() -ne ''} | Set-Content .flake8"
2025-05-31 11:50:41,365 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:832 - _execute_command - 🔧 执行命令: powershell -Command "(Get-Content .flake8 -ErrorAction SilentlyContinue) -replace '#.*', '' | Where-Object {$_.trim() -ne ''} | Set-Content .flake8"
2025-05-31 11:50:41,366 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:833 - _execute_command - 📁 工作目录: E:\aider-git-repos\ai-proxy
2025-05-31 11:50:45,616 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:887 - _execute_command - ⏱️ 命令执行时间: 4.25秒
2025-05-31 11:50:45,617 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:888 - _execute_command - 🔢 返回码: 1
2025-05-31 11:50:45,617 - bot_agent.tools.intelligent_fix_coordinator - WARNING - intelligent_fix_coordinator.py:893 - _execute_command - ⚠️ 错误输出: #< CLIXML
所在位置 行:1 字符: 94
+ ... n SilentlyContinue) -replace '#.*', '' | Where-Object {.trim() -ne '' ...
+                                                                  ~
“(”后面应为表达式。
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId...
2025-05-31 11:50:45,622 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 4 for session task_1748663371: 第4轮：自动化修复命令 1
2025-05-31 11:50:45,622 - bot_agent.tools.intelligent_fix_coordinator - WARNING - intelligent_fix_coordinator.py:527 - _execute_automated_fix - 自动化修复成功率不理想 (0.0%)，自动切换到Aider执行器...
2025-05-31 11:50:45,623 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:581 - _execute_aider_fix_with_context - 🧠 执行Aider AI修复策略（带自动化失败上下文）...
2025-05-31 11:50:45,625 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 5 for session task_1748663371: 第5轮：Aider智能兜底修复启动
2025-05-31 11:50:45,626 - bot_agent.executors.aider_executor - INFO - aider_executor.py:92 - execute_plan - 🚀 开始执行Aider计划...
2025-05-31 11:50:45,626 - bot_agent.executors.aider_executor - INFO - aider_executor.py:93 - execute_plan -    - 会话ID: task_1748663371
2025-05-31 11:50:45,627 - bot_agent.executors.aider_executor - INFO - aider_executor.py:94 - execute_plan -    - 任务类型: intelligent_fix_with_context
2025-05-31 11:50:45,627 - bot_agent.executors.aider_executor - INFO - aider_executor.py:95 - execute_plan -    - 复杂度: simple
2025-05-31 11:50:45,628 - bot_agent.executors.aider_executor - INFO - aider_executor.py:98 - execute_plan -    - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-31 11:50:45,628 - bot_agent.executors.aider_executor - INFO - aider_executor.py:102 - execute_plan -    - 执行步骤数量: 1
2025-05-31 11:50:45,629 - bot_agent.executors.aider_executor - INFO - aider_executor.py:107 - execute_plan -    - 指令长度: 1270 字符
2025-05-31 11:50:45,629 - bot_agent.executors.aider_executor - INFO - aider_executor.py:108 - execute_plan -    - 指令预览: 
## 🎯 修复任务说明（自动化修复失败后的Aider接管）

### 🚨 自动化修复失败情况
自动化命令执行失败，成功率仅 0.0%

### 💥 失败的自动化命令
- powershell -Co...
2025-05-31 11:50:45,630 - bot_agent.config.model_config - DEBUG - model_config.py:56 - get_code_generation_model - 获取代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-31 11:50:45,630 - bot_agent.executors.aider_executor - INFO - aider_executor.py:191 - _create_aider_instance - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-31 11:50:45,678 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:55 - log_operation - [AIDER_MONITOR] ✅ coder_created: 创建监控的Coder
2025-05-31 11:50:45,678 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   kwargs: {'main_model': Model(name='openrouter/deepseek/deepseek-chat-v3-0324:free', edit_format='diff', weak_model_name='openrouter/deepseek/deepseek-chat-v3-0324:free', use_repo_map=True, send_undo_reply=False, lazy=False, overeager=False, reminder='user', examples_as_sys_msg=True, extra_params=None, cache_control=False, caches_by_default=True, use_system_prompt=True, use_temperature=0.3, streaming=True, editor_model_name='openrouter/deepseek/deepseek-r1:free', editor_edit_format='editor-diff', reasoning_tag=None, remove_reasoning=None, system_prompt_prefix=None, accepts_settings=[]), 'io': <bot_agent.aider_extensions.aider_monitor.MonitoredIO object at 0x000001D93FCDE9F0>, 'fnames': [], 'use_git': True, 'stream': False, 'verbose': False, 'chat_language': 'Chinese', 'auto_accept_architect': True}
2025-05-31 11:50:45,679 - bot_agent.executors.aider_executor - INFO - aider_executor.py:231 - _create_aider_instance - ✅ 创建了带监控的Aider实例，会话ID: task_1748663371
2025-05-31 11:50:45,680 - bot_agent.executors.aider_executor - INFO - aider_executor.py:149 - execute_plan - 📋 准备执行 1 个步骤
2025-05-31 11:50:45,680 - bot_agent.executors.aider_executor - INFO - aider_executor.py:264 - _execute_step - 🔧 执行步骤 1: AI智能修复（自动化失败后接管） - simple级别
2025-05-31 11:50:45,681 - bot_agent.executors.aider_executor - INFO - aider_executor.py:268 - _execute_step - 📝 Aider执行指令: 
请修复以下错误：code_fix_with_context

修复要求：
- 直接修复代码，不要询问
- 保持代码风格一致
- 确保修复后代码能正常运行
- 用中文简要说明修复内容

开始修复：AI智能修复（自动化失败后接管） - simple级别
...
2025-05-31 11:50:45,681 - bot_agent.executors.aider_executor - INFO - aider_executor.py:279 - _execute_step - 🚀 调用aider_coder.run()方法
2025-05-31 11:50:45,682 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:55 - log_operation - [AIDER_MONITOR] ✅ coder_run_start: 开始Coder执行
2025-05-31 11:50:45,682 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   message: 
请修复以下错误：code_fix_with_context

修复要求：
- 直接修复代码，不要询问
- 保持代码风格一致
- 确保修复后代码能正常运行
- 用中文简要说明修复内容

开始修复：AI智能修复（自动化失败后接管） - simple级别

2025-05-31 11:50:45,683 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   kwargs: {}
2025-05-31 11:50:45,686 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 6 for session task_1748663371: 第6轮：Aider监控 - coder_run_start
2025-05-31 11:51:06,032 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:55 - log_operation - [AIDER_MONITOR] ✅ tool_output: 工具输出
2025-05-31 11:51:06,033 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   messages: []
2025-05-31 11:51:06,033 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   message_content: 
2025-05-31 11:51:06,033 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   kwargs: {}
2025-05-31 11:51:06,037 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 7 for session task_1748663371: 第7轮：Aider监控 - tool_output
2025-05-31 11:51:06,037 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:55 - log_operation - [AIDER_MONITOR] ✅ tool_output: 工具输出
2025-05-31 11:51:06,038 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   messages: ['Tokens: 3.5k sent, 109 received.']
2025-05-31 11:51:06,039 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   message_content: Tokens: 3.5k sent, 109 received.
2025-05-31 11:51:06,039 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   kwargs: {}
2025-05-31 11:51:06,042 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 8 for session task_1748663371: 第8轮：Aider监控 - tool_output
2025-05-31 11:51:06,046 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:55 - log_operation - [AIDER_MONITOR] ✅ coder_run_complete: Coder执行完成
2025-05-31 11:51:06,046 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   result_length: 464
2025-05-31 11:51:06,047 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:59 - log_operation - [AIDER_MONITOR]   result_preview: Got it! I'll follow these rules when helping you with code changes. 

When you have a specific file you'd like to modify, please:
1. Share the full file path and contents
2. Describe what changes you'...
2025-05-31 11:51:06,052 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 9 for session task_1748663371: 第9轮：Aider监控 - coder_run_complete
2025-05-31 11:51:06,052 - bot_agent.executors.aider_executor - INFO - aider_executor.py:290 - _execute_step - ✅ Aider执行完成，耗时: 20.37秒
2025-05-31 11:51:06,053 - bot_agent.executors.aider_executor - INFO - aider_executor.py:291 - _execute_step - 📤 Aider响应: Got it! I'll follow these rules when helping you with code changes. 

When you have a specific file you'd like to modify, please:
1. Share the full file path and contents
2. Describe what changes you'd like to make
3. I'll then provide the specific SEARCH/REPLACE blocks needed

I won't make any assu...
2025-05-31 11:51:06,056 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 10 for session task_1748663371: 第10轮：Aider执行完成 1 - AI智能修复（自动化失败后接管） - simple级别
2025-05-31 11:51:06,056 - bot_agent.executors.aider_executor - INFO - aider_executor.py:157 - execute_plan - ✅ 步骤 1 执行成功
2025-05-31 11:51:06,060 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 11 for session task_1748663371: 第11轮：Aider智能兜底修复完成
2025-05-31 11:51:06,063 - bot_agent.tools.intelligent_fix_validator - INFO - intelligent_fix_validator.py:62 - validate_fix - 🔍 开始智能修复验证...
2025-05-31 11:51:06,064 - bot_agent.tools.intelligent_fix_validator - INFO - intelligent_fix_validator.py:134 - _validate_original_commands - 🔧 验证命令: flake8 --config .flake8
2025-05-31 11:51:06,065 - bot_agent.tools.intelligent_fix_validator - INFO - intelligent_fix_validator.py:134 - _validate_original_commands - 🔧 验证命令: black --check --config pyproject.toml .
2025-05-31 11:51:06,065 - bot_agent.tools.intelligent_fix_validator - INFO - intelligent_fix_validator.py:103 - validate_fix - ✅ 修复验证完成: failed (成功率: 0.0%)
2025-05-31 11:51:06,077 - bot_agent.tools.fix_learning_engine - INFO - fix_learning_engine.py:50 - load_learning_data - ✅ 加载了 3 个修复模式
2025-05-31 11:51:06,078 - bot_agent.tools.fix_learning_engine - INFO - fix_learning_engine.py:64 - save_learning_data - 💾 保存了 3 个修复模式
2025-05-31 11:51:06,079 - bot_agent.tools.fix_learning_engine - INFO - fix_learning_engine.py:169 - record_fix_attempt - 📚 记录修复尝试: job:lint|types:flake8_config_error,generic_job_failure|keywords:extend-ignore -> automated_fallback_to_aider (成功)
2025-05-31 11:51:06,080 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1703 - verify_fixes - ✅ 开始验证修复效果...
2025-05-31 11:51:08,683 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:164 - execute_command - 命令执行完成: powershell -Command "black --check --config pyproject.toml ."
2025-05-31 11:51:08,684 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:165 - execute_command - 返回码: 0, 成功: True
2025-05-31 11:51:08,684 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:166 - execute_command - 执行时间: 2.60秒
2025-05-31 11:51:08,685 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check --config pyproject.toml ."
2025-05-31 11:51:11,148 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:164 - execute_command - 命令执行完成: powershell -Command "flake8 ."
2025-05-31 11:51:11,149 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:165 - execute_command - 返回码: 1, 成功: False
2025-05-31 11:51:11,149 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:166 - execute_command - 执行时间: 2.46秒
2025-05-31 11:51:11,149 - bot_agent.tools.terminal_tools - WARNING - terminal_tools.py:171 - execute_command - 错误输出: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\s...
2025-05-31 11:51:11,150 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-31 11:51:11,151 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-31 11:51:13,455 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:164 - execute_command - 命令执行完成: powershell -EncodedCommand cAB5AHQAaABvAG4AIAAtAG0AIABwAHkAXwBjAG8AbQBwAGkAbABlACAAIgBlAHgAYQBtAHAAbABlAC4AcAB5ACIAIAAiAHMAZQB0AHUAcAAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGMAbwBuAGYAaQBnAC4AcAB5ACIAIAAiAGEAcABpAF8AcAByAG8AeAB5AFwAaABlAGEAbAB0AGgAXwBjAGgAZQBjAGsALgBwAHkAIgAgACIAYQBwAGkAXwBwAHIAbwB4AHkAXABqAG8AYgBfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AZgBhAGkAbAB1AHIAZQBfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AbABpAG4AdABfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AbABpAG4AdABfAHMAZQByAHYAaQBjAGUALgBwAHkAIgAgACIAYQBwAGkAXwBwAHIAbwB4AHkAXABtAG8AZABlAGwAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAG0AbwBuAGkAdABvAHIAaQBuAGcALgBwAHkAIgA=
2025-05-31 11:51:13,456 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:165 - execute_command - 返回码: 0, 成功: True
2025-05-31 11:51:13,456 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:166 - execute_command - 执行时间: 2.30秒
2025-05-31 11:51:13,457 - bot_agent.tools.terminal_tools - WARNING - terminal_tools.py:171 - execute_command - 错误输出: #< CLIXML
<Objs Version="1.1.0.1" xmlns="http://schemas.microsoft.com/powershell/2004/04"><Obj S="progress" RefId="0"><TN RefId="0"><T>System.Management.Automation.PSCustomObject</T><T>System.Object</T></TN><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><Obj S="progress" RefId="1"><TNRef RefId="0" /><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块。</AV><AI>0</AI><Nil /><PI>-1</P...
2025-05-31 11:51:13,457 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -EncodedCommand cAB5AHQAaABvAG4AIAAtAG0AIABwAHkAXwBjAG8AbQBwAGkAbABlACAAIgBlAHgAYQBtAHAAbABlAC4AcAB5ACIAIAAiAHMAZQB0AHUAcAAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGMAbwBuAGYAaQBnAC4AcAB5ACIAIAAiAGEAcABpAF8AcAByAG8AeAB5AFwAaABlAGEAbAB0AGgAXwBjAGgAZQBjAGsALgBwAHkAIgAgACIAYQBwAGkAXwBwAHIAbwB4AHkAXABqAG8AYgBfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AZgBhAGkAbAB1AHIAZQBfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AbABpAG4AdABfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AbABpAG4AdABfAHMAZQByAHYAaQBjAGUALgBwAHkAIgAgACIAYQBwAGkAXwBwAHIAbwB4AHkAXABtAG8AZABlAGwAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAG0AbwBuAGkAdABvAHIAaQBuAGcALgBwAHkAIgA=
2025-05-31 11:51:13,458 - bot_agent.config.model_config - DEBUG - model_config.py:44 - get_analysis_model - 获取推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-31 11:51:13,458 - bot_agent.config.model_config - DEBUG - model_config.py:84 - convert_aider_to_openrouter_format - 转换模型名称: openrouter/deepseek/deepseek-r1:free -> deepseek/deepseek-r1:free
2025-05-31 11:51:13,459 - bot_agent.config.model_config - INFO - model_config.py:141 - log_current_config - 当前AI模型配置:
2025-05-31 11:51:13,459 - bot_agent.config.model_config - DEBUG - model_config.py:44 - get_analysis_model - 获取推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-31 11:51:13,459 - bot_agent.config.model_config - INFO - model_config.py:142 - log_current_config -   - 推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-31 11:51:13,460 - bot_agent.config.model_config - DEBUG - model_config.py:56 - get_code_generation_model - 获取代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-31 11:51:13,460 - bot_agent.config.model_config - INFO - model_config.py:143 - log_current_config -   - 代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-31 11:51:13,461 - bot_agent.config.model_config - DEBUG - model_config.py:68 - get_chat_model - 获取通用对话模型: openrouter/deepseek/deepseek-chat
2025-05-31 11:51:13,461 - bot_agent.config.model_config - INFO - model_config.py:144 - log_current_config -   - 通用对话模型: openrouter/deepseek/deepseek-chat
2025-05-31 11:51:13,461 - bot_agent.config.model_config - INFO - model_config.py:145 - log_current_config -   - API密钥已设置: 是
2025-05-31 11:51:13,462 - bot_agent.config.model_config - DEBUG - model_config.py:44 - get_analysis_model - 获取推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-31 11:51:13,462 - bot_agent.config.model_config - DEBUG - model_config.py:56 - get_code_generation_model - 获取代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-31 11:51:13,463 - bot_agent.config.model_config - INFO - model_config.py:135 - validate_config - 模型配置验证结果: {'api_key': True, 'analysis_model': True, 'code_generation_model': True}
2025-05-31 11:51:13,463 - bot_agent.config.model_config - INFO - model_config.py:150 - log_current_config - ✅ 所有模型配置正常
2025-05-31 11:51:13,464 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:48 - __init__ - AI Task analyzer initialized with analysis model: deepseek/deepseek-r1:free
2025-05-31 11:51:51,177 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:181 - _ai_analyze_task - AI分析响应: ```json
{
  "task_type": "project_analysis",
  "confidence": 0.8,
  "priority": "medium",
  "complexity": "low",
  "reasoning": "该任务属于对修复效果的验证分析，涉及检查修复结果是否成功及遗留问题评估。由于修复步骤数为0且成功修复数为0，说明未实际执行修复操作，仅进行了结果验证。需要分析修复是否彻底、遗留问题及后续建议，属于项目状态评估而非直接代码修改。",
  "risks": [
    {
      "type": "unresolved_issues",
      "level": "medium",
      "description": "验证成功率为50%，表明部分问题未解决，存在功能未完全修复的风险"
    }
  ]
}
```
2025-05-31 11:51:51,179 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:197 - _ai_analyze_task - AI任务分析成功: {'task_type': 'project_analysis', 'confidence': 0.8, 'priority': 'medium', 'complexity': 'low', 'reasoning': '该任务属于对修复效果的验证分析，涉及检查修复结果是否成功及遗留问题评估。由于修复步骤数为0且成功修复数为0，说明未实际执行修复操作，仅进行了结果验证。需要分析修复是否彻底、遗留问题及后续建议，属于项目状态评估而非直接代码修改。', 'risks': [{'type': 'unresolved_issues', 'level': 'medium', 'description': '验证成功率为50%，表明部分问题未解决，存在功能未完全修复的风险'}]}
2025-05-31 11:51:51,183 - bot_agent.utils.conversation_logger - DEBUG - conversation_logger.py:245 - log_round - Logged round 12 for session task_1748663371: 第12轮：修复效果验证
2025-05-31 11:51:51,184 - bot_agent.engines.task_executor - INFO - task_executor.py:1435 - _handle_job_failure_analysis - ✅ 智能修复和验证流程完成
2025-05-31 11:51:51,184 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - AI智能分析完成
2025-05-31 11:51:51,185 - bot_agent.engines.task_executor - INFO - task_executor.py:1491 - _handle_job_failure_analysis - ✅ AI智能分析完成，保留原始AI响应
2025-05-31 11:51:51,189 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:281 - end_session - Ended conversation session: task_1748663371, status: ConversationStatus.SUCCESS
2025-05-31 11:51:51,190 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: job_failure_analysis，执行时间: 139.88s
2025-05-31 11:51:51,646 - bot_agent.engines.task_executor - INFO - task_executor.py:316 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 1032)
2025-05-31 11:51:56,930 - bot_agent.engines.task_executor - INFO - task_executor.py:342 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-31 11:51:56,932 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:62 - analyze_task_completion - Analyzed task completion: 6 global, 1 project memories
2025-05-31 11:51:56,934 - bot_agent.memory.global_memory - INFO - global_memory.py:90 - save_work_habit - Work habit saved: task_preference
2025-05-31 11:51:56,943 - bot_agent.memory.global_memory - INFO - global_memory.py:90 - save_work_habit - Work habit saved: tool_preference
2025-05-31 11:51:56,945 - bot_agent.memory.global_memory - INFO - global_memory.py:136 - save_common_pattern - Common pattern saved: file_creation
2025-05-31 11:51:56,954 - bot_agent.memory.global_memory - INFO - global_memory.py:136 - save_common_pattern - Common pattern saved: directory_structure
2025-05-31 11:51:56,955 - bot_agent.memory.global_memory - INFO - global_memory.py:109 - save_preference - Preference saved: programming_languages
2025-05-31 11:51:56,964 - bot_agent.memory.global_memory - INFO - global_memory.py:109 - save_preference - Preference saved: programming_languages
2025-05-31 11:51:56,967 - bot_agent.memory.project_memory - INFO - project_memory.py:94 - save_coding_standard - Coding standard saved: general
2025-05-31 11:51:56,969 - bot_agent.memory.global_memory - INFO - global_memory.py:150 - save_environment_info - Environment info saved
2025-05-31 11:51:56,969 - bot_agent.memory.memory_integration - INFO - memory_integration.py:111 - learn_from_task_completion - Learned from task completion: 6 global, 1 project memories
2025-05-31 11:51:56,969 - bot_agent.handlers.ai_response_handler - WARNING - ai_response_handler.py:104 - _handle_gitlab_response - Unsupported event type: Job Hook
2025-05-31 11:51:56,970 - bot_agent.dispatcher.router - INFO - router.py:111 - _dispatch_to_component - Task 184c0021-2c5f-472b-904b-8f5000d22eb0 processed by AI processor: success
2025-05-31 11:51:56,970 - bot_agent.webhook.gitlab - INFO - gitlab.py:1237 - handle_job_event - Job event task routed: {'task_id': '184c0021-2c5f-472b-904b-8f5000d22eb0', 'target_component': 'aider', 'status': 'accepted', 'message': 'Task 184c0021-2c5f-472b-904b-8f5000d22eb0 accepted and processed'}
2025-05-31 11:51:56,971 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'success', 'message': 'Job lint (failed) routed to AI for critical_job_failure', 'task_id': '184c0021-2c5f-472b-904b-8f5000d22eb0', 'processing_reason': 'critical_job_failure'}
2025-05-31 11:51:59,050 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-31 11:51:59,050 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-31 11:51:59,051 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-31 11:51:59,051 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '86d995be-997a-4c5e-9faa-cafedfaee8b9', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'f2b2c31e-f748-4dfc-bc77-0eff51c8f314', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '2c1a5dc1-20ab-4006-8d03-1d0eb243bb9a', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-31 11:51:59,052 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-31 11:51:59,052 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-31 11:51:59,053 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-31 11:51:59,053 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-31 11:51:59,054 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '86d995be-997a-4c5e-9faa-cafedfaee8b9', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'f2b2c31e-f748-4dfc-bc77-0eff51c8f314', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '2c1a5dc1-20ab-4006-8d03-1d0eb243bb9a', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-31 11:51:59,054 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'retries_count': 0, 'build_id': 1033, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'created', 'build_created_at': '2025-05-31 03:52:00 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-31T03:52:00Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 277, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 277, 'name': None, 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1032)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-31 11:51:59,056 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-31 11:51:59,056 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-31 11:51:59,057 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (1033) in stage test is created (Pipeline: 277, Project: ai-proxy, User: Longer)
2025-05-31 11:51:59,057 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status created recorded (no AI processing needed)
2025-05-31 11:51:59,058 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status created recorded'}
2025-05-31 11:51:59,291 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-31 11:51:59,292 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-31 11:51:59,292 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-31 11:51:59,293 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '3dcd4aa1-f8b0-4238-af7a-e9c8932f4720', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'c174eb5e-b097-4277-ad86-c0396a7bea8e', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '56aa142b-5044-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-31 11:51:59,293 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-31 11:51:59,294 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-31 11:51:59,294 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-31 11:51:59,295 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-31 11:51:59,295 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '3dcd4aa1-f8b0-4238-af7a-e9c8932f4720', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'c174eb5e-b097-4277-ad86-c0396a7bea8e', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '56aa142b-5044-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-31 11:51:59,296 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'retries_count': 0, 'build_id': 1034, 'build_name': 'lint', 'build_stage': 'test', 'build_status': 'created', 'build_created_at': '2025-05-31 03:52:00 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-31T03:52:00Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 277, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 277, 'name': None, 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1032)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-31 11:51:59,297 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-31 11:51:59,298 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-31 11:51:59,298 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job lint (1034) in stage test is created (Pipeline: 277, Project: ai-proxy, User: Longer)
2025-05-31 11:51:59,299 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job lint status created recorded (no AI processing needed)
2025-05-31 11:51:59,299 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job lint status created recorded'}
2025-05-31 11:51:59,574 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-31 11:51:59,574 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-31 11:51:59,575 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-31 11:51:59,575 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '12a05c63-7b9a-4e15-9a95-7ec500b3d23a', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '0891528a-13a8-4d8b-871b-a1f3ee5ab6d2', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'f88518c3-1228-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1939'}
2025-05-31 11:51:59,576 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-31 11:51:59,576 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-31 11:51:59,577 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-31 11:51:59,577 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-31 11:51:59,578 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '12a05c63-7b9a-4e15-9a95-7ec500b3d23a', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '0891528a-13a8-4d8b-871b-a1f3ee5ab6d2', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'f88518c3-1228-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1939'}
2025-05-31 11:51:59,579 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'retries_count': 0, 'build_id': 1035, 'build_name': 'build', 'build_stage': 'build', 'build_status': 'created', 'build_created_at': '2025-05-31 03:52:00 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-31T03:52:00Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 277, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 277, 'name': None, 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1032)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-31 11:51:59,580 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-31 11:51:59,581 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-31 11:51:59,581 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job build (1035) in stage build is created (Pipeline: 277, Project: ai-proxy, User: Longer)
2025-05-31 11:51:59,581 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job build status created recorded (no AI processing needed)
2025-05-31 11:51:59,582 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job build status created recorded'}
2025-05-31 11:52:03,772 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-31 11:52:03,773 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-31 11:52:03,774 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-31 11:52:03,774 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'bf5798fe-2ade-4dc1-aa97-2a207411ce8f', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '1e1a0636-add4-42d2-9521-d42cfd1218a5', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'f4bf741a-e2a0-4dde-ab6b-7643cfd96403', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1944'}
2025-05-31 11:52:03,775 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-31 11:52:03,775 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-31 11:52:03,776 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-31 11:52:03,776 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-31 11:52:03,776 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'bf5798fe-2ade-4dc1-aa97-2a207411ce8f', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '1e1a0636-add4-42d2-9521-d42cfd1218a5', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'f4bf741a-e2a0-4dde-ab6b-7643cfd96403', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1944'}
2025-05-31 11:52:03,777 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'retries_count': 0, 'build_id': 1033, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'pending', 'build_created_at': '2025-05-31 03:52:00 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-31T03:52:00Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': 0.647293639, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 277, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 277, 'name': None, 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1032)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-31 11:52:03,779 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-31 11:52:03,779 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-31 11:52:03,779 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (1033) in stage test is pending (Pipeline: 277, Project: ai-proxy, User: Longer)
2025-05-31 11:52:03,780 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status pending recorded (no AI processing needed)
2025-05-31 11:52:03,780 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status pending recorded'}
2025-05-31 11:52:05,321 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-31 11:52:05,322 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-31 11:52:05,322 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-31 11:52:05,322 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '924da852-3865-4856-aa7b-bee1e3e78aaf', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'be8addc5-8dd2-40ab-9015-b886dd20216a', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '2bfeac53-ccd1-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1944'}
2025-05-31 11:52:05,323 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-31 11:52:05,324 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-31 11:52:05,324 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-31 11:52:05,324 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-31 11:52:05,325 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '924da852-3865-4856-aa7b-bee1e3e78aaf', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'be8addc5-8dd2-40ab-9015-b886dd20216a', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '2bfeac53-ccd1-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1944'}
2025-05-31 11:52:05,326 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'retries_count': 0, 'build_id': 1034, 'build_name': 'lint', 'build_stage': 'test', 'build_status': 'pending', 'build_created_at': '2025-05-31 03:52:00 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-31T03:52:00Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': 0.805977409, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 277, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 277, 'name': None, 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1032)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-31 11:52:05,327 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-31 11:52:05,328 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-31 11:52:05,328 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job lint (1034) in stage test is pending (Pipeline: 277, Project: ai-proxy, User: Longer)
2025-05-31 11:52:05,328 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job lint status pending recorded (no AI processing needed)
2025-05-31 11:52:05,329 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job lint status pending recorded'}
2025-05-31 11:52:09,267 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-31 11:52:09,267 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-31 11:52:09,268 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-31 11:52:09,269 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '798dc549-0de1-4157-9edd-1a4f8e66a840', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '53243c82-7de4-41d6-9382-861a9ed2b338', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'b4285fe6-fd4b-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3249'}
2025-05-31 11:52:09,269 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-31 11:52:09,270 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-31 11:52:09,270 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-31 11:52:09,271 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-31 11:52:09,271 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '798dc549-0de1-4157-9edd-1a4f8e66a840', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '53243c82-7de4-41d6-9382-861a9ed2b338', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'b4285fe6-fd4b-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3249'}
2025-05-31 11:52:09,272 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 277, 'iid': 99, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'before_sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'source': 'push', 'status': 'pending', 'detailed_status': 'pending', 'stages': ['test', 'build'], 'created_at': '2025-05-31 03:52:00 UTC', 'finished_at': None, 'duration': None, 'queued_duration': None, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/277'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '8123bad1d368d8c5f120862de47308d7748bada6', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1032)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 1032)', 'timestamp': '2025-05-31T11:51:51+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/8123bad1d368d8c5f120862de47308d7748bada6', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 1035, 'stage': 'build', 'name': 'build', 'status': 'created', 'created_at': '2025-05-31 03:52:00 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 1034, 'stage': 'test', 'name': 'lint', 'status': 'pending', 'created_at': '2025-05-31 03:52:00 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': 4.994863512, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 1033, 'stage': 'test', 'name': 'test', 'status': 'running', 'created_at': '2025-05-31 03:52:00 UTC', 'started_at': '2025-05-31 03:52:07 UTC', 'finished_at': None, 'duration': 1.85703271, 'queued_duration': 4.715318, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-31 11:52:09,274 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-31 11:52:09,274 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-31 11:52:09,275 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 277 for aider-plus-dev is pending (Project: ai-proxy, User: Longer)
2025-05-31 11:52:09,275 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 277 status pending recorded (no AI monitoring needed)
2025-05-31 11:52:09,276 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 277 status pending recorded'}
2025-05-31 11:52:10,505 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-31 11:52:10,506 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-31 11:52:10,506 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-31 11:52:10,507 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '013e6644-827e-4ac8-841d-8151b9e0d826', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '989d00ac-9774-4d56-aaa3-68a9d6eaceb3', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '5db2d7b4-f596-43d8-aa3e-1e3c02a800ee', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2108'}
2025-05-31 11:52:10,507 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-31 11:52:10,508 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-31 11:52:10,508 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-31 11:52:10,509 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-31 11:52:10,509 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '013e6644-827e-4ac8-841d-8151b9e0d826', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '989d00ac-9774-4d56-aaa3-68a9d6eaceb3', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '5db2d7b4-f596-43d8-aa3e-1e3c02a800ee', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2108'}
2025-05-31 11:52:10,510 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'retries_count': 0, 'build_id': 1033, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'running', 'build_created_at': '2025-05-31 03:52:00 UTC', 'build_started_at': '2025-05-31 03:52:07 UTC', 'build_finished_at': None, 'build_created_at_iso': '2025-05-31T03:52:00Z', 'build_started_at_iso': '2025-05-31T03:52:07Z', 'build_finished_at_iso': None, 'build_duration': 0.538655755, 'build_queued_duration': 4.715318781, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 277, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 277, 'name': None, 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1032)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'pending', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-31 11:52:10,511 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-31 11:52:10,512 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-31 11:52:10,512 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (1033) in stage test is running (Pipeline: 277, Project: ai-proxy, User: Longer)
2025-05-31 11:52:10,513 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status running recorded (no AI processing needed)
2025-05-31 11:52:10,513 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status running recorded'}
2025-05-31 11:52:12,410 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-31 11:52:12,410 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-31 11:52:12,411 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-31 11:52:12,412 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'a8cc3667-9e3e-4869-9de2-f5cc782e826c', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': 'fa5b1668-fcc4-4c7b-a237-2bd88b5f2590', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'ee7515cd-20b7-45cf-b5f4-11aacf12dd54', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3248'}
2025-05-31 11:52:12,412 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-31 11:52:12,413 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-31 11:52:12,413 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-31 11:52:12,414 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-31 11:52:12,414 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'a8cc3667-9e3e-4869-9de2-f5cc782e826c', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': 'fa5b1668-fcc4-4c7b-a237-2bd88b5f2590', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'ee7515cd-20b7-45cf-b5f4-11aacf12dd54', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3248'}
2025-05-31 11:52:12,415 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 277, 'iid': 99, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '8123bad1d368d8c5f120862de47308d7748bada6', 'before_sha': 'c3b89fa142b5d5cf1bc144dbed6ba3d35405170c', 'source': 'push', 'status': 'running', 'detailed_status': 'running', 'stages': ['test', 'build'], 'created_at': '2025-05-31 03:52:00 UTC', 'finished_at': None, 'duration': None, 'queued_duration': 12, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/277'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '8123bad1d368d8c5f120862de47308d7748bada6', 'message': 'AI自动修改: 作业失败分析 - lint (Job 1032)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 1032)', 'timestamp': '2025-05-31T11:51:51+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/8123bad1d368d8c5f120862de47308d7748bada6', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 1035, 'stage': 'build', 'name': 'build', 'status': 'created', 'created_at': '2025-05-31 03:52:00 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 1034, 'stage': 'test', 'name': 'lint', 'status': 'pending', 'created_at': '2025-05-31 03:52:00 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': 9.906748397, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 1033, 'stage': 'test', 'name': 'test', 'status': 'running', 'created_at': '2025-05-31 03:52:00 UTC', 'started_at': '2025-05-31 03:52:07 UTC', 'finished_at': None, 'duration': 6.768924522, 'queued_duration': 4.715318, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-31 11:52:12,417 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-31 11:52:12,417 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-31 11:52:12,418 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 277 for aider-plus-dev is running (Project: ai-proxy, User: Longer)
2025-05-31 11:52:12,418 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 277 status running recorded (no AI monitoring needed)
2025-05-31 11:52:12,419 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 277 status running recorded'}
2025-05-31 11:53:28,599 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📊 当前监控状态:
2025-05-31 11:53:28,599 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log -    无活跃监控点
