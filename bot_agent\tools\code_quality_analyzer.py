"""
代码质量分析器 - 深度分析代码质量并提供改进建议

核心功能：
1. 代码复杂度分析
2. 安全漏洞检测
3. 性能问题识别
4. 代码重构建议
5. 最佳实践检查
"""

import logging
import ast
import os
import subprocess
import json
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import re

logger = logging.getLogger(__name__)

class QualityIssueType(Enum):
    """代码质量问题类型"""
    COMPLEXITY = "complexity"           # 复杂度问题
    SECURITY = "security"              # 安全问题
    PERFORMANCE = "performance"        # 性能问题
    MAINTAINABILITY = "maintainability" # 可维护性问题
    STYLE = "style"                    # 代码风格问题
    DOCUMENTATION = "documentation"    # 文档问题
    TESTING = "testing"               # 测试问题

class QualitySeverity(Enum):
    """质量问题严重程度"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

@dataclass
class QualityIssue:
    """代码质量问题"""
    type: QualityIssueType
    severity: QualitySeverity
    file_path: str
    line_number: int
    message: str
    suggestion: str
    code_snippet: Optional[str] = None
    metric_value: Optional[float] = None

@dataclass
class QualityReport:
    """代码质量报告"""
    overall_score: float  # 0-100
    issues: List[QualityIssue]
    metrics: Dict[str, Any]
    suggestions: List[str]
    refactoring_opportunities: List[Dict[str, Any]]

class CodeQualityAnalyzer:
    """代码质量分析器"""

    def __init__(self):
        self.complexity_threshold = 10
        self.line_length_threshold = 88
        self.function_length_threshold = 50

    async def analyze_project_quality(self, project_path: str,
                                    focus_files: List[str] = None) -> QualityReport:
        """分析项目代码质量"""
        try:
            logger.info(f"🔍 开始代码质量分析: {project_path}")

            # 获取要分析的文件
            if focus_files:
                python_files = [f for f in focus_files if f.endswith('.py')]
            else:
                python_files = self._get_python_files(project_path)

            all_issues = []
            all_metrics = {}

            # 分析每个Python文件
            for file_path in python_files:
                try:
                    file_issues, file_metrics = await self._analyze_file(file_path)
                    all_issues.extend(file_issues)
                    all_metrics[file_path] = file_metrics
                except Exception as e:
                    logger.error(f"分析文件失败 {file_path}: {e}")

            # 运行外部工具分析
            external_issues = await self._run_external_tools(project_path, python_files)
            all_issues.extend(external_issues)

            # 计算整体质量分数
            overall_score = self._calculate_quality_score(all_issues, all_metrics)

            # 生成改进建议
            suggestions = self._generate_suggestions(all_issues, all_metrics)

            # 识别重构机会
            refactoring_opportunities = self._identify_refactoring_opportunities(all_issues, all_metrics)

            report = QualityReport(
                overall_score=overall_score,
                issues=all_issues,
                metrics=all_metrics,
                suggestions=suggestions,
                refactoring_opportunities=refactoring_opportunities
            )

            logger.info(f"✅ 代码质量分析完成，总分: {overall_score:.1f}")
            return report

        except Exception as e:
            logger.error(f"代码质量分析失败: {e}")
            return QualityReport(
                overall_score=0.0,
                issues=[],
                metrics={},
                suggestions=[f"分析失败: {str(e)}"],
                refactoring_opportunities=[]
            )

    def _get_python_files(self, project_path: str) -> List[str]:
        """获取项目中的Python文件"""
        python_files = []

        for root, dirs, files in os.walk(project_path):
            # 跳过虚拟环境和缓存目录
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', 'venv', '.venv', 'node_modules']]

            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    python_files.append(file_path)

        return python_files

    async def _analyze_file(self, file_path: str) -> Tuple[List[QualityIssue], Dict[str, Any]]:
        """分析单个文件"""
        issues = []
        metrics = {}

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()

            # 解析AST
            try:
                tree = ast.parse(content)
            except SyntaxError as e:
                issues.append(QualityIssue(
                    type=QualityIssueType.STYLE,
                    severity=QualitySeverity.CRITICAL,
                    file_path=file_path,
                    line_number=e.lineno or 1,
                    message=f"语法错误: {e.msg}",
                    suggestion="修复语法错误"
                ))
                return issues, metrics

            # 复杂度分析
            complexity_issues, complexity_metrics = self._analyze_complexity(tree, file_path, lines)
            issues.extend(complexity_issues)
            metrics.update(complexity_metrics)

            # 代码风格分析
            style_issues = self._analyze_style(lines, file_path)
            issues.extend(style_issues)

            # 安全性分析
            security_issues = self._analyze_security(tree, file_path, content)
            issues.extend(security_issues)

            # 性能分析
            performance_issues = self._analyze_performance(tree, file_path, content)
            issues.extend(performance_issues)

            # 文档分析
            doc_issues = self._analyze_documentation(tree, file_path)
            issues.extend(doc_issues)

            # 基本指标
            metrics.update({
                'lines_of_code': len(lines),
                'blank_lines': sum(1 for line in lines if not line.strip()),
                'comment_lines': sum(1 for line in lines if line.strip().startswith('#')),
                'functions': len([node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]),
                'classes': len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
            })

        except Exception as e:
            logger.error(f"分析文件失败 {file_path}: {e}")
            issues.append(QualityIssue(
                type=QualityIssueType.MAINTAINABILITY,
                severity=QualitySeverity.HIGH,
                file_path=file_path,
                line_number=1,
                message=f"文件分析失败: {str(e)}",
                suggestion="检查文件编码和格式"
            ))

        return issues, metrics

    def _analyze_complexity(self, tree: ast.AST, file_path: str, lines: List[str]) -> Tuple[List[QualityIssue], Dict[str, Any]]:
        """分析代码复杂度"""
        issues = []
        metrics = {}

        class ComplexityVisitor(ast.NodeVisitor):
            def __init__(self):
                self.complexity_scores = []
                self.long_functions = []

            def visit_FunctionDef(self, node):
                # 计算圈复杂度
                complexity = self._calculate_cyclomatic_complexity(node)
                self.complexity_scores.append(complexity)

                if complexity > self.complexity_threshold:
                    issues.append(QualityIssue(
                        type=QualityIssueType.COMPLEXITY,
                        severity=QualitySeverity.HIGH if complexity > 15 else QualitySeverity.MEDIUM,
                        file_path=file_path,
                        line_number=node.lineno,
                        message=f"函数 '{node.name}' 复杂度过高: {complexity}",
                        suggestion="考虑将函数拆分为更小的函数",
                        metric_value=complexity
                    ))

                # 检查函数长度
                func_length = (node.end_lineno or node.lineno) - node.lineno + 1
                if func_length > self.function_length_threshold:
                    issues.append(QualityIssue(
                        type=QualityIssueType.MAINTAINABILITY,
                        severity=QualitySeverity.MEDIUM,
                        file_path=file_path,
                        line_number=node.lineno,
                        message=f"函数 '{node.name}' 过长: {func_length} 行",
                        suggestion="考虑将长函数拆分为多个小函数",
                        metric_value=func_length
                    ))

                self.generic_visit(node)

            def _calculate_cyclomatic_complexity(self, node):
                """计算圈复杂度"""
                complexity = 1  # 基础复杂度

                for child in ast.walk(node):
                    if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                        complexity += 1
                    elif isinstance(child, ast.ExceptHandler):
                        complexity += 1
                    elif isinstance(child, ast.BoolOp):
                        complexity += len(child.values) - 1

                return complexity

        visitor = ComplexityVisitor()
        visitor.visit(tree)

        if visitor.complexity_scores:
            metrics['avg_complexity'] = sum(visitor.complexity_scores) / len(visitor.complexity_scores)
            metrics['max_complexity'] = max(visitor.complexity_scores)

        return issues, metrics

    def _analyze_style(self, lines: List[str], file_path: str) -> List[QualityIssue]:
        """分析代码风格"""
        issues = []

        for i, line in enumerate(lines, 1):
            # 检查行长度
            if len(line) > self.line_length_threshold:
                issues.append(QualityIssue(
                    type=QualityIssueType.STYLE,
                    severity=QualitySeverity.LOW,
                    file_path=file_path,
                    line_number=i,
                    message=f"行过长: {len(line)} 字符",
                    suggestion=f"将行长度控制在 {self.line_length_threshold} 字符以内",
                    code_snippet=line[:100] + "..." if len(line) > 100 else line
                ))

            # 检查尾随空格
            if line.endswith(' ') or line.endswith('\t'):
                issues.append(QualityIssue(
                    type=QualityIssueType.STYLE,
                    severity=QualitySeverity.LOW,
                    file_path=file_path,
                    line_number=i,
                    message="行末有多余空格",
                    suggestion="移除行末空格"
                ))

        return issues

    def _analyze_security(self, tree: ast.AST, file_path: str, content: str) -> List[QualityIssue]:
        """分析安全问题"""
        issues = []

        # 检查危险的函数调用
        dangerous_functions = ['eval', 'exec', 'compile', '__import__']

        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name) and node.func.id in dangerous_functions:
                    issues.append(QualityIssue(
                        type=QualityIssueType.SECURITY,
                        severity=QualitySeverity.HIGH,
                        file_path=file_path,
                        line_number=node.lineno,
                        message=f"使用了危险函数: {node.func.id}",
                        suggestion="避免使用动态代码执行函数，考虑更安全的替代方案"
                    ))

        # 检查硬编码密码
        password_patterns = [
            r'password\s*=\s*["\'][^"\']+["\']',
            r'pwd\s*=\s*["\'][^"\']+["\']',
            r'secret\s*=\s*["\'][^"\']+["\']'
        ]

        for pattern in password_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                issues.append(QualityIssue(
                    type=QualityIssueType.SECURITY,
                    severity=QualitySeverity.MEDIUM,
                    file_path=file_path,
                    line_number=line_num,
                    message="可能包含硬编码密码",
                    suggestion="使用环境变量或配置文件存储敏感信息"
                ))

        return issues

    def _analyze_performance(self, tree: ast.AST, file_path: str, content: str) -> List[QualityIssue]:
        """分析性能问题"""
        issues = []

        # 检查循环中的字符串拼接
        for node in ast.walk(tree):
            if isinstance(node, (ast.For, ast.While)):
                for child in ast.walk(node):
                    if isinstance(child, ast.AugAssign) and isinstance(child.op, ast.Add):
                        if isinstance(child.target, ast.Name):
                            issues.append(QualityIssue(
                                type=QualityIssueType.PERFORMANCE,
                                severity=QualitySeverity.MEDIUM,
                                file_path=file_path,
                                line_number=child.lineno,
                                message="循环中使用字符串拼接可能影响性能",
                                suggestion="考虑使用 join() 方法或 f-string"
                            ))

        return issues

    def _analyze_documentation(self, tree: ast.AST, file_path: str) -> List[QualityIssue]:
        """分析文档问题"""
        issues = []

        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                if not ast.get_docstring(node):
                    issues.append(QualityIssue(
                        type=QualityIssueType.DOCUMENTATION,
                        severity=QualitySeverity.LOW,
                        file_path=file_path,
                        line_number=node.lineno,
                        message=f"{type(node).__name__} '{node.name}' 缺少文档字符串",
                        suggestion="添加描述功能和参数的文档字符串"
                    ))

        return issues

    async def _run_external_tools(self, project_path: str, python_files: List[str]) -> List[QualityIssue]:
        """运行外部代码质量工具"""
        issues = []

        # 运行 bandit 安全检查
        try:
            result = subprocess.run(
                ['bandit', '-r', project_path, '-f', 'json'],
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode == 0 and result.stdout:
                bandit_data = json.loads(result.stdout)
                for result_item in bandit_data.get('results', []):
                    issues.append(QualityIssue(
                        type=QualityIssueType.SECURITY,
                        severity=QualitySeverity.HIGH if result_item['issue_severity'] == 'HIGH' else QualitySeverity.MEDIUM,
                        file_path=result_item['filename'],
                        line_number=result_item['line_number'],
                        message=f"安全问题: {result_item['issue_text']}",
                        suggestion=result_item.get('issue_confidence', '建议修复此安全问题')
                    ))
        except (subprocess.TimeoutExpired, FileNotFoundError, json.JSONDecodeError):
            pass  # bandit 不可用或执行失败

        return issues

    def _calculate_quality_score(self, issues: List[QualityIssue], metrics: Dict[str, Any]) -> float:
        """计算代码质量分数"""
        base_score = 100.0

        # 根据问题严重程度扣分
        severity_penalties = {
            QualitySeverity.CRITICAL: 20,
            QualitySeverity.HIGH: 10,
            QualitySeverity.MEDIUM: 5,
            QualitySeverity.LOW: 2,
            QualitySeverity.INFO: 1
        }

        for issue in issues:
            penalty = severity_penalties.get(issue.severity, 1)
            base_score -= penalty

        return max(0.0, min(100.0, base_score))

    def _generate_suggestions(self, issues: List[QualityIssue], metrics: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        suggestions = []

        # 按问题类型分组
        issue_counts = {}
        for issue in issues:
            issue_counts[issue.type] = issue_counts.get(issue.type, 0) + 1

        # 生成针对性建议
        if issue_counts.get(QualityIssueType.COMPLEXITY, 0) > 0:
            suggestions.append("🔧 考虑重构复杂函数，将其拆分为更小的函数")

        if issue_counts.get(QualityIssueType.SECURITY, 0) > 0:
            suggestions.append("🔒 修复安全问题，避免使用危险函数和硬编码敏感信息")

        if issue_counts.get(QualityIssueType.PERFORMANCE, 0) > 0:
            suggestions.append("⚡ 优化性能问题，特别是循环中的字符串操作")

        if issue_counts.get(QualityIssueType.DOCUMENTATION, 0) > 0:
            suggestions.append("📚 添加文档字符串，提高代码可读性")

        if issue_counts.get(QualityIssueType.STYLE, 0) > 0:
            suggestions.append("🎨 统一代码风格，使用 black 或 autopep8 格式化代码")

        return suggestions

    def _identify_refactoring_opportunities(self, issues: List[QualityIssue], metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """识别重构机会"""
        opportunities = []

        # 识别高复杂度函数
        complex_functions = [issue for issue in issues
                           if issue.type == QualityIssueType.COMPLEXITY and issue.metric_value and issue.metric_value > 15]

        if complex_functions:
            opportunities.append({
                'type': 'function_decomposition',
                'title': '函数分解重构',
                'description': f'发现 {len(complex_functions)} 个高复杂度函数需要重构',
                'files': list(set(issue.file_path for issue in complex_functions)),
                'priority': 'high'
            })

        # 识别重复代码模式
        # 这里可以添加更复杂的重复代码检测逻辑

        return opportunities


# 全局代码质量分析器实例
global_code_quality_analyzer = CodeQualityAnalyzer()
