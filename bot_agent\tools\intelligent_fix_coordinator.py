"""
智能修复协调器 - 整合智能路由器和执行器

核心思想：
1. 推理模型分析问题复杂度 → 智能路由决策
2. 简单问题 → 自动化执行器（快速修复）
3. 复杂问题 → Aider执行器（AI深度思考）
4. 充分发挥每个组件的优势
"""

import logging
from typing import Dict, Any, Optional
import time
import asyncio

from .intelligent_fix_router import IntelligentFixRouter, FixStrategy, FixComplexity
from ..executors.aider_executor import AiderExecutor
from ..utils.conversation_logger import global_conversation_logger, ConversationStatus
from .performance_monitor import global_performance_monitor

logger = logging.getLogger(__name__)

class IntelligentFixCoordinator:
    """智能修复协调器"""

    def __init__(self):
        self.router = IntelligentFixRouter()
        self.aider_executor = AiderExecutor()
        # 失败学习机制
        self.failure_memory = {}  # 记录失败的命令和原因
        self.success_patterns = {}  # 记录成功的修复模式
        logger.info("IntelligentFixCoordinator initialized with learning capabilities")

    async def execute_intelligent_fix(self, error_analysis: Dict[str, Any],
                                    project_path: str,
                                    session_id: str,
                                    job_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行智能修复流程

        Args:
            error_analysis: 错误分析结果
            project_path: 项目路径
            session_id: 会话ID
            job_info: 作业信息

        Returns:
            修复结果
        """
        try:
            logger.info("🚀 启动智能修复协调器...")

            # 第1步：智能路由分析（带性能监控和缓存）
            start_time = time.time()

            # 使用缓存的项目上下文收集
            async def gather_context():
                return await self._gather_project_context(project_path, job_info)

            project_context = await global_performance_monitor.cached_operation(
                operation_name="gather_project_context",
                cache_key_data={'project_path': project_path, 'job_info': job_info},
                operation_func=gather_context
            )

            # 使用受控的AI调用进行路由分析
            async def route_analysis():
                return await self.router.analyze_and_route(error_analysis, project_context, session_id)

            fix_decision = await global_performance_monitor.controlled_ai_call(
                operation_name="intelligent_fix_routing",
                ai_call_func=route_analysis
            )

            routing_duration = time.time() - start_time

            # 记录完整的路由决策（包含AI调用信息）
            ai_model_used = getattr(fix_decision, '_ai_model_used', 'intelligent-fix-router')
            execution_plan = getattr(fix_decision, '_execution_plan', '执行计划生成中...')
            analysis_method = getattr(fix_decision, '_analysis_method', 'AI推理分析')
            fallback_reason = getattr(fix_decision, '_fallback_reason', None)

            # 确保显示真实的AI模型名，而不是组件名
            display_model_name = ai_model_used
            if ai_model_used == 'intelligent-fix-router':
                # 如果是组件名，尝试获取真实的AI模型名
                display_model_name = 'deepseek/deepseek-r1:free'  # 默认使用的推理模型

            # 根据是否使用fallback决定响应内容和状态
            if fallback_reason:
                # AI调用失败，使用了fallback方案
                response_content = f"""## ⚠️ 智能修复路由决策 (AI调用异常)

### 🚨 AI调用状态
- **目标模型**: deepseek/deepseek-r1:free
- **调用状态**: ❌ 失败
- **失败原因**: {fallback_reason}
- **处理方式**: 自动切换到规则分析

### 🔧 Fallback分析结果
- **分析方式**: 规则引擎分析
- **复杂度**: {fix_decision.complexity.value}
- **策略**: {fix_decision.strategy.value}
- **置信度**: {fix_decision.confidence:.2f} (规则生成)
- **预估时间**: {fix_decision.estimated_time}秒
- **需要审查**: {fix_decision.requires_human_review}

### 📝 决策理由
{fix_decision.reasoning}

### 📝 执行计划
{execution_plan}

### ⚠️ 重要说明
此决策基于规则分析生成，非AI推理结果。建议人工审查。
"""
                conversation_status = ConversationStatus.FAILED
                display_model_name = "rule-based-fallback"
            else:
                # AI调用成功
                response_content = f"""## 🧠 智能修复路由决策

### 🤖 AI分析信息
- **使用模型**: {ai_model_used}
- **分析方式**: {analysis_method}
- **调用状态**: ✅ 成功

### 📊 分析结果
- **复杂度**: {fix_decision.complexity.value}
- **策略**: {fix_decision.strategy.value}
- **置信度**: {fix_decision.confidence:.2f}
- **预估时间**: {fix_decision.estimated_time}秒
- **需要审查**: {fix_decision.requires_human_review}

### 🎯 决策理由
{fix_decision.reasoning}

### 📝 执行计划
{execution_plan}
"""
                conversation_status = ConversationStatus.SUCCESS

            global_conversation_logger.log_round(
                session_id=session_id,
                round_name=f"智能修复路由决策",
                prompt=f"错误分析: {error_analysis}",
                response=response_content,
                model_name=display_model_name,
                duration=routing_duration,
                status=conversation_status
            )

            # 第2步：根据策略执行修复
            if fix_decision.strategy == FixStrategy.AUTOMATED:
                return await self._execute_automated_fix(fix_decision, project_path, session_id)
            elif fix_decision.strategy == FixStrategy.AIDER_GUIDED:
                return await self._execute_aider_fix(fix_decision, project_path, session_id, error_analysis)
            else:  # HYBRID
                return await self._execute_hybrid_fix(fix_decision, project_path, session_id, error_analysis)

        except Exception as e:
            logger.error(f"智能修复协调器执行失败: {e}")
            return {
                'success': False,
                'message': f'智能修复协调器失败: {str(e)}',
                'strategy': 'error',
                'complexity': 'unknown',
                'execution_time': 0
            }

    async def _gather_project_context(self, project_path: str, job_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """收集深度项目上下文信息"""
        try:
            import os
            import json
            from pathlib import Path

            context = {
                'project_path': project_path,
                'project_type': 'python',  # 默认
                'job_info': job_info or {}
            }

            # 1. 检测项目类型和结构
            project_files = self._scan_project_files(project_path)
            context.update(project_files)

            # 2. 读取配置文件
            config_info = await self._read_config_files(project_path)
            context['configurations'] = config_info

            # 3. 分析依赖信息
            dependency_info = await self._analyze_dependencies(project_path, context['project_type'])
            context['dependencies'] = dependency_info

            # 4. 收集Git信息
            git_info = await self._collect_git_info(project_path)
            context['git_info'] = git_info

            # 5. 分析代码质量配置
            quality_config = self._analyze_quality_config(project_path)
            context['quality_config'] = quality_config

            return context

        except Exception as e:
            logger.error(f"收集项目上下文失败: {e}")
            return {'project_path': project_path, 'project_type': 'unknown'}

    def _scan_project_files(self, project_path: str) -> Dict[str, Any]:
        """扫描项目文件结构"""
        import os

        files_info = {
            'has_tests': False,
            'has_docs': False,
            'has_ci_config': False,
            'config_files': [],
            'project_type': 'python'
        }

        try:
            # 检查常见目录
            for root, dirs, files in os.walk(project_path):
                # 只扫描前两层目录
                level = root.replace(project_path, '').count(os.sep)
                if level > 2:
                    continue

                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, project_path)

                    # 检测项目类型
                    if file == 'package.json':
                        files_info['project_type'] = 'javascript'
                    elif file == 'Cargo.toml':
                        files_info['project_type'] = 'rust'
                    elif file == 'go.mod':
                        files_info['project_type'] = 'go'
                    elif file == 'pom.xml':
                        files_info['project_type'] = 'java'

                    # 检测配置文件
                    config_files = [
                        '.flake8', 'setup.cfg', 'pyproject.toml', 'tox.ini',
                        '.editorconfig', '.gitignore', '.pre-commit-config.yaml',
                        'requirements.txt', 'requirements-dev.txt', 'Pipfile',
                        'docker-compose.yml', 'Dockerfile', '.gitlab-ci.yml'
                    ]

                    if file in config_files:
                        files_info['config_files'].append(rel_path)

                    # 检测CI配置
                    if file in ['.gitlab-ci.yml', '.github', 'Jenkinsfile', '.travis.yml']:
                        files_info['has_ci_config'] = True

                # 检查目录
                for dir_name in dirs:
                    if dir_name in ['tests', 'test', '__tests__']:
                        files_info['has_tests'] = True
                    elif dir_name in ['docs', 'doc', 'documentation']:
                        files_info['has_docs'] = True

        except Exception as e:
            logger.error(f"扫描项目文件失败: {e}")

        return files_info

    async def _read_config_files(self, project_path: str) -> Dict[str, Any]:
        """读取配置文件内容"""
        import os
        import configparser
        import json

        configs = {}

        try:
            # 读取 .flake8 配置
            flake8_path = os.path.join(project_path, '.flake8')
            if os.path.exists(flake8_path):
                try:
                    config = configparser.ConfigParser()
                    config.read(flake8_path)
                    configs['flake8'] = dict(config.items('flake8')) if config.has_section('flake8') else {}
                except Exception as e:
                    logger.warning(f"读取.flake8配置失败: {e}")

            # 读取 pyproject.toml 配置
            pyproject_path = os.path.join(project_path, 'pyproject.toml')
            if os.path.exists(pyproject_path):
                try:
                    import toml
                    with open(pyproject_path, 'r', encoding='utf-8') as f:
                        configs['pyproject'] = toml.load(f)
                except Exception as e:
                    logger.warning(f"读取pyproject.toml配置失败: {e}")

            # 读取 package.json 配置
            package_json_path = os.path.join(project_path, 'package.json')
            if os.path.exists(package_json_path):
                try:
                    with open(package_json_path, 'r', encoding='utf-8') as f:
                        configs['package_json'] = json.load(f)
                except Exception as e:
                    logger.warning(f"读取package.json配置失败: {e}")

        except Exception as e:
            logger.error(f"读取配置文件失败: {e}")

        return configs

    async def _analyze_dependencies(self, project_path: str, project_type: str) -> Dict[str, Any]:
        """分析项目依赖"""
        import os

        dependencies = {
            'main_dependencies': [],
            'dev_dependencies': [],
            'python_version': None,
            'dependency_files': []
        }

        try:
            if project_type == 'python':
                # 分析 requirements.txt
                req_path = os.path.join(project_path, 'requirements.txt')
                if os.path.exists(req_path):
                    dependencies['dependency_files'].append('requirements.txt')
                    with open(req_path, 'r', encoding='utf-8') as f:
                        dependencies['main_dependencies'] = [
                            line.strip() for line in f.readlines()
                            if line.strip() and not line.startswith('#')
                        ]

                # 分析 requirements-dev.txt
                req_dev_path = os.path.join(project_path, 'requirements-dev.txt')
                if os.path.exists(req_dev_path):
                    dependencies['dependency_files'].append('requirements-dev.txt')
                    with open(req_dev_path, 'r', encoding='utf-8') as f:
                        dependencies['dev_dependencies'] = [
                            line.strip() for line in f.readlines()
                            if line.strip() and not line.startswith('#')
                        ]

        except Exception as e:
            logger.error(f"分析依赖失败: {e}")

        return dependencies

    async def _collect_git_info(self, project_path: str) -> Dict[str, Any]:
        """收集Git信息"""
        git_info = {
            'is_git_repo': False,
            'current_branch': None,
            'recent_commits': [],
            'modified_files': []
        }

        try:
            import subprocess

            # 检查是否是Git仓库
            result = subprocess.run(
                ['git', 'rev-parse', '--git-dir'],
                cwd=project_path,
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                git_info['is_git_repo'] = True

                # 获取当前分支
                result = subprocess.run(
                    ['git', 'branch', '--show-current'],
                    cwd=project_path,
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    git_info['current_branch'] = result.stdout.strip()

                # 获取最近的提交
                result = subprocess.run(
                    ['git', 'log', '--oneline', '-5'],
                    cwd=project_path,
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    git_info['recent_commits'] = result.stdout.strip().split('\n')

        except Exception as e:
            logger.warning(f"收集Git信息失败: {e}")

        return git_info

    def _analyze_quality_config(self, project_path: str) -> Dict[str, Any]:
        """分析代码质量配置"""
        import os

        quality_config = {
            'has_linting': False,
            'has_formatting': False,
            'has_type_checking': False,
            'linting_tools': [],
            'formatting_tools': [],
            'type_checking_tools': []
        }

        try:
            # 检查配置文件
            if os.path.exists(os.path.join(project_path, '.flake8')):
                quality_config['has_linting'] = True
                quality_config['linting_tools'].append('flake8')

            if os.path.exists(os.path.join(project_path, 'pyproject.toml')):
                # 检查 pyproject.toml 中的工具配置
                try:
                    import toml
                    with open(os.path.join(project_path, 'pyproject.toml'), 'r') as f:
                        config = toml.load(f)

                    if 'tool' in config:
                        tools = config['tool']

                        if 'black' in tools:
                            quality_config['has_formatting'] = True
                            quality_config['formatting_tools'].append('black')

                        if 'mypy' in tools:
                            quality_config['has_type_checking'] = True
                            quality_config['type_checking_tools'].append('mypy')

                        if 'flake8' in tools:
                            quality_config['has_linting'] = True
                            quality_config['linting_tools'].append('flake8')

                except Exception:
                    pass

        except Exception as e:
            logger.error(f"分析代码质量配置失败: {e}")

        return quality_config

    async def _execute_automated_fix(self, fix_decision, project_path: str, session_id: str) -> Dict[str, Any]:
        """执行自动化修复"""
        try:
            logger.info("🤖 执行自动化修复策略...")

            start_time = time.time()
            commands = fix_decision.automated_commands or []

            logger.info(f"🚀 开始执行自动化修复...")
            logger.info(f"   - 项目路径: {project_path}")
            logger.info(f"   - 会话ID: {session_id}")
            logger.info(f"   - 命令数量: {len(commands)}")
            logger.info(f"   - 修复策略: {fix_decision.strategy}")
            logger.info(f"   - 复杂度: {fix_decision.complexity}")

            if commands:
                logger.info(f"   - 待执行命令:")
                for i, cmd in enumerate(commands):
                    logger.info(f"     {i+1}. {cmd}")
            else:
                logger.warning(f"   ❌ 没有可执行的自动化命令")

            if not commands:
                logger.warning("没有可执行的自动化命令，立即切换到Aider执行器...")

                # 构建空命令失败的上下文
                failure_context = {
                    'failed_commands': [],
                    'original_error_analysis': fix_decision,
                    'automation_failure_reason': '没有生成可执行的自动化命令',
                    'command_outputs': [],
                    'command_errors': [],
                    'detailed_error_analysis': {'total_commands': 0, 'failed_commands': 0, 'error_types': [], 'detailed_errors': []},
                    'error_patterns': [],
                    'suggested_fixes': []
                }

                # 切换到Aider执行器
                aider_result = await self._execute_aider_fix_with_context(
                    fix_decision, project_path, session_id, failure_context
                )

                return {
                    'success': aider_result['success'],
                    'message': f'自动化命令为空，已切换到Aider: {aider_result["message"]}',
                    'strategy': 'automated_fallback_to_aider',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': aider_result.get('execution_time', 0),
                    'automation_results': [],
                    'aider_result': aider_result,
                    'success_rate': 0.0
                }

            # 执行自动化命令
            results = []
            for i, command in enumerate(commands):
                logger.info(f"🔧 执行命令 {i+1}/{len(commands)}: {command}")

                result = await self._execute_command(command, project_path)
                results.append(result)

                # 记录每个命令的执行
                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_name=f"自动化修复命令 {i+1}",
                    prompt=f"执行命令: {command}",
                    response=f"执行结果: {'成功' if result['success'] else '失败'}\n输出: {result.get('output', '')}",
                    model_name="automated-executor",
                    duration=result.get('duration', 0),
                    status=ConversationStatus.SUCCESS if result['success'] else ConversationStatus.FAILED
                )

            execution_time = time.time() - start_time
            successful_commands = sum(1 for r in results if r['success'])
            success_rate = successful_commands / len(commands) if commands else 0

            # 如果自动化修复失败，立即切换到Aider（降低阈值，提高Aider介入频率）
            if success_rate < 0.8:  # 从0.5提高到0.8，更容易触发Aider介入
                logger.warning(f"自动化修复成功率不理想 ({success_rate:.1%})，自动切换到Aider执行器...")

                # 构建增强的失败上下文，包含详细错误信息
                failure_context = {
                    'failed_commands': [r for r in results if not r['success']],
                    'original_error_analysis': fix_decision,
                    'automation_failure_reason': f'自动化命令执行失败，成功率仅 {success_rate:.1%}',
                    'command_outputs': [r.get('output', '') for r in results],
                    'command_errors': [r.get('error', '') for r in results if not r['success']],
                    # 增强的错误分析
                    'detailed_error_analysis': self._analyze_command_errors(results),
                    'error_patterns': self._extract_error_patterns(results),
                    'suggested_fixes': self._suggest_error_fixes(results)
                }

                # 切换到Aider执行器
                aider_result = await self._execute_aider_fix_with_context(
                    fix_decision, project_path, session_id, failure_context
                )

                return {
                    'success': aider_result['success'],
                    'message': f'自动化修复失败，已切换到Aider: {aider_result["message"]}',
                    'strategy': 'automated_fallback_to_aider',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time + aider_result.get('execution_time', 0),
                    'automation_results': results,
                    'aider_result': aider_result,
                    'success_rate': success_rate
                }

            return {
                'success': successful_commands > 0,
                'message': f'自动化修复执行了 {len(commands)} 个命令，成功 {successful_commands} 个',
                'strategy': 'automated',
                'complexity': fix_decision.complexity.value,
                'execution_time': execution_time,
                'command_results': results,
                'success_rate': success_rate
            }

        except Exception as e:
            logger.error(f"自动化修复执行失败: {e}")
            return {
                'success': False,
                'message': f'自动化修复失败: {str(e)}',
                'strategy': 'automated',
                'complexity': fix_decision.complexity.value,
                'execution_time': 0
            }

    async def _execute_aider_fix_with_context(self, fix_decision, project_path: str, session_id: str, failure_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行带失败上下文的Aider修复"""
        try:
            logger.info("🧠 执行Aider AI修复策略（带自动化失败上下文）...")

            # 记录Aider兜底开始到会话日志
            from bot_agent.utils.conversation_logger import global_conversation_logger, ConversationStatus

            global_conversation_logger.log_round(
                session_id=session_id,
                round_name="Aider智能兜底修复启动",
                prompt=f"""## 🚨 自动化修复失败，启动Aider智能兜底

### 💥 失败原因
{failure_context.get('automation_failure_reason', '自动化修复失败')}

### 🔧 失败的命令
{chr(10).join([f"- {cmd['command']}: {cmd.get('error', '执行失败')}" for cmd in failure_context.get('failed_commands', [])])}

### 🎯 原始问题
{fix_decision.aider_instructions or '需要AI深度分析和修复'}
""",
                response="正在启动Aider智能修复引擎...",
                model_name="aider-coordinator",
                duration=0.0,
                status=ConversationStatus.IN_PROGRESS
            )

            start_time = time.time()

            # 构建增强的Aider执行计划，包含失败上下文
            enhanced_instructions = f"""
## 🎯 修复任务说明（自动化修复失败后的Aider接管）

### 🚨 自动化修复失败情况
{failure_context.get('automation_failure_reason', '自动化修复失败')}

### 💥 失败的自动化命令
{chr(10).join([f"- {cmd['command']}: {cmd.get('error', '执行失败')}" for cmd in failure_context.get('failed_commands', [])])}

### 🎯 原始问题分析
{fix_decision.aider_instructions or '需要AI深度分析和修复'}

### 🔧 修复要求
1. 分析自动化修复失败的原因
2. 理解原始问题的本质
3. 提供更智能的解决方案
4. 确保修复的完整性和正确性
5. 避免简单的命令执行，使用AI理解和代码生成

请用中文详细说明你的修复思路，然后实施修复。
"""

            execution_plan = {
                'task_type': 'intelligent_fix_with_context',
                'complexity': fix_decision.complexity.value,
                'instructions': enhanced_instructions,
                'project_path': project_path,
                'failure_context': failure_context,
                'steps': [
                    {
                        'step_id': 1,
                        'description': f'AI智能修复（自动化失败后接管） - {fix_decision.complexity.value}级别',
                        'target': 'code_fix_with_context',
                        'instructions': enhanced_instructions
                    }
                ]
            }

            # 使用Aider执行器
            if self.aider_executor.aider_available:
                result = await self.aider_executor.execute_plan(execution_plan, session_id)

                execution_time = time.time() - start_time

                # 记录Aider兜底执行结果到会话日志
                status = ConversationStatus.SUCCESS if result.success else ConversationStatus.FAILED

                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_name="Aider智能兜底修复完成",
                    prompt="Aider执行计划完成",
                    response=f"""## 🤖 Aider智能兜底修复结果

### 📊 执行统计
- **执行状态**: {'✅ 成功' if result.success else '❌ 失败'}
- **执行时间**: {execution_time:.2f}秒
- **成功步骤**: {len(result.executed_steps)}
- **失败步骤**: {len(result.failed_steps)}
- **总执行时间**: {result.execution_time:.2f}秒

### 📝 执行详情
{result.message}

### 🔧 修复策略
- **策略类型**: aider_guided_with_context
- **复杂度**: {fix_decision.complexity.value}
- **上下文提供**: 是

### 📋 执行步骤详情
成功执行了 {len(result.executed_steps)} 个步骤，失败 {len(result.failed_steps)} 个步骤。

### 🎯 修复结果
{'Aider智能修复成功完成，问题已解决。' if result.success else 'Aider修复过程中遇到问题，需要进一步处理。'}
""",
                    model_name="aider-coordinator",
                    duration=execution_time,
                    status=status
                )

                return {
                    'success': result.success,
                    'message': f'Aider接管修复完成: {result.message}',
                    'strategy': 'aider_guided_with_context',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time,
                    'aider_result': {
                        'executed_steps': len(result.executed_steps),
                        'failed_steps': len(result.failed_steps),
                        'total_time': result.execution_time
                    },
                    'context_provided': True
                }
            else:
                logger.error("Aider不可用，无法进行智能修复")
                return {
                    'success': False,
                    'message': 'Aider不可用，无法进行智能修复',
                    'strategy': 'aider_unavailable',
                    'execution_time': 0
                }

        except Exception as e:
            logger.error(f"带上下文的Aider修复执行失败: {e}")
            return {
                'success': False,
                'message': f'带上下文的Aider修复失败: {str(e)}',
                'strategy': 'aider_guided_with_context',
                'execution_time': 0
            }

    async def _execute_aider_fix(self, fix_decision, project_path: str, session_id: str, error_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """执行Aider修复"""
        try:
            logger.info("🧠 执行Aider AI修复策略...")

            start_time = time.time()

            # 构建Aider执行计划
            execution_plan = {
                'task_type': 'intelligent_fix',
                'complexity': fix_decision.complexity.value,
                'instructions': fix_decision.aider_instructions,
                'project_path': project_path,
                'steps': [
                    {
                        'step_id': 1,
                        'description': f'AI智能修复 - {fix_decision.complexity.value}级别',
                        'target': 'code_fix',
                        'instructions': fix_decision.aider_instructions
                    }
                ]
            }

            # 使用Aider执行器
            if self.aider_executor.aider_available:
                result = await self.aider_executor.execute_plan(execution_plan, session_id)

                execution_time = time.time() - start_time

                return {
                    'success': result.success,
                    'message': f'Aider修复完成: {result.message}',
                    'strategy': 'aider_guided',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time,
                    'aider_result': {
                        'executed_steps': len(result.executed_steps),
                        'failed_steps': len(result.failed_steps),
                        'total_time': result.execution_time
                    }
                }
            else:
                # Aider不可用，回退到自动化策略
                logger.warning("Aider不可用，回退到自动化修复")
                return await self._execute_automated_fix(fix_decision, project_path, session_id)

        except Exception as e:
            logger.error(f"Aider修复执行失败: {e}")
            return {
                'success': False,
                'message': f'Aider修复失败: {str(e)}',
                'strategy': 'aider_guided',
                'complexity': fix_decision.complexity.value,
                'execution_time': 0
            }

    async def _execute_hybrid_fix(self, fix_decision, project_path: str, session_id: str, error_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """执行混合修复策略"""
        try:
            logger.info("🔄 执行混合修复策略...")

            start_time = time.time()

            # 先执行自动化修复
            auto_result = await self._execute_automated_fix(fix_decision, project_path, session_id)

            # 如果自动化修复不完全成功，使用Aider补充（降低阈值，更容易触发）
            if not auto_result['success'] or auto_result.get('success_rate', 0) < 0.9:
                logger.info("自动化修复不完全成功，启动Aider补充修复...")
                aider_result = await self._execute_aider_fix(fix_decision, project_path, session_id, error_analysis)

                execution_time = time.time() - start_time

                return {
                    'success': auto_result['success'] or aider_result['success'],
                    'message': f'混合修复完成 - 自动化: {auto_result["message"]}, Aider: {aider_result["message"]}',
                    'strategy': 'hybrid',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time,
                    'auto_result': auto_result,
                    'aider_result': aider_result
                }
            else:
                execution_time = time.time() - start_time
                return {
                    'success': True,
                    'message': '自动化修复已成功，无需Aider补充',
                    'strategy': 'hybrid_auto_only',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time,
                    'auto_result': auto_result
                }

        except Exception as e:
            logger.error(f"混合修复执行失败: {e}")
            return {
                'success': False,
                'message': f'混合修复失败: {str(e)}',
                'strategy': 'hybrid',
                'complexity': fix_decision.complexity.value,
                'execution_time': 0
            }

    async def _execute_command(self, command: str, project_path: str) -> Dict[str, Any]:
        """执行单个命令"""
        try:
            import subprocess
            import time
            import platform
            import os

            start_time = time.time()

            logger.info(f"🔧 执行命令: {command}")
            logger.info(f"📁 工作目录: {project_path}")

            # 验证项目路径是否存在
            if not os.path.exists(project_path):
                logger.error(f"❌ 项目路径不存在: {project_path}")
                return {
                    'success': False,
                    'output': '',
                    'error': f'项目路径不存在: {project_path}',
                    'return_code': -1,
                    'duration': 0,
                    'command': command
                }

            # 验证是否有权限访问项目路径
            if not os.access(project_path, os.R_OK | os.W_OK):
                logger.error(f"❌ 没有项目路径的读写权限: {project_path}")
                return {
                    'success': False,
                    'output': '',
                    'error': f'没有项目路径的读写权限: {project_path}',
                    'return_code': -1,
                    'duration': 0,
                    'command': command
                }

            # 根据操作系统选择执行方式
            if platform.system().lower() == 'windows':
                # 尝试多种PowerShell执行方式
                success, result = await self._execute_powershell_command(command, project_path)
                if not success:
                    # 如果PowerShell失败，尝试cmd
                    logger.warning("PowerShell执行失败，尝试使用cmd")
                    result = subprocess.run(
                        ["cmd", "/c", command],
                        cwd=project_path,
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
            else:
                # Linux/Mac使用bash
                result = subprocess.run(
                    command,
                    cwd=project_path,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    shell=True
                )

            duration = time.time() - start_time

            # 详细日志记录
            logger.info(f"⏱️ 命令执行时间: {duration:.2f}秒")
            logger.info(f"🔢 返回码: {result.returncode}")

            if result.stdout:
                logger.info(f"📤 标准输出: {result.stdout[:300]}...")
            if result.stderr:
                logger.warning(f"⚠️ 错误输出: {result.stderr[:300]}...")

            return {
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr,
                'return_code': result.returncode,
                'duration': duration,
                'command': command
            }

        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'output': '',
                'error': '命令执行超时',
                'return_code': -1,
                'duration': 60,
                'command': command
            }
        except Exception as e:
            return {
                'success': False,
                'output': '',
                'error': str(e),
                'return_code': -1,
                'duration': 0,
                'command': command
            }

    async def _execute_powershell_command(self, command: str, project_path: str) -> tuple:
        """执行PowerShell命令，尝试多种方式"""
        import subprocess

        # 方式1: 直接执行简单命令
        if not ('"' in command or "'" in command or '|' in command):
            try:
                result = subprocess.run(
                    ["powershell", "-Command", command],
                    cwd=project_path,
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                return True, result
            except Exception as e:
                logger.warning(f"简单PowerShell命令执行失败: {e}")

        # 方式2: 使用Base64编码
        try:
            import base64
            encoded_command = base64.b64encode(command.encode('utf-16le')).decode('ascii')
            result = subprocess.run(
                ["powershell", "-EncodedCommand", encoded_command],
                cwd=project_path,
                capture_output=True,
                text=True,
                timeout=60
            )
            return True, result
        except Exception as e:
            logger.warning(f"Base64编码PowerShell命令执行失败: {e}")

        # 方式3: 写入临时脚本文件
        try:
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(mode='w', suffix='.ps1', delete=False) as f:
                f.write(command)
                script_path = f.name

            try:
                result = subprocess.run(
                    ["powershell", "-ExecutionPolicy", "Bypass", "-File", script_path],
                    cwd=project_path,
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                return True, result
            finally:
                os.unlink(script_path)

        except Exception as e:
            logger.warning(f"临时脚本PowerShell命令执行失败: {e}")

        return False, None

    def _analyze_command_errors(self, results: list) -> Dict[str, Any]:
        """分析命令执行错误，提取关键信息"""
        error_analysis = {
            'total_commands': len(results),
            'failed_commands': len([r for r in results if not r['success']]),
            'error_types': [],
            'common_patterns': [],
            'detailed_errors': []
        }

        for result in results:
            if not result['success']:
                error_info = {
                    'command': result.get('command', ''),
                    'return_code': result.get('return_code', -1),
                    'error_message': result.get('error', ''),
                    'output': result.get('output', ''),
                    'duration': result.get('duration', 0)
                }

                # 分析错误类型
                error_msg = result.get('error', '').lower()
                if 'valueerror' in error_msg:
                    error_info['error_type'] = 'ValueError'
                elif 'filenotfounderror' in error_msg:
                    error_info['error_type'] = 'FileNotFoundError'
                elif 'permissionerror' in error_msg:
                    error_info['error_type'] = 'PermissionError'
                elif 'timeout' in error_msg:
                    error_info['error_type'] = 'TimeoutError'
                elif 'flake8' in error_msg:
                    error_info['error_type'] = 'Flake8ConfigError'
                else:
                    error_info['error_type'] = 'UnknownError'

                error_analysis['detailed_errors'].append(error_info)

                if error_info['error_type'] not in error_analysis['error_types']:
                    error_analysis['error_types'].append(error_info['error_type'])

        return error_analysis

    def _extract_error_patterns(self, results: list) -> list:
        """提取错误模式"""
        patterns = []

        for result in results:
            if not result['success']:
                error_msg = result.get('error', '')

                # 检查常见错误模式
                if "Error code '#' supplied to 'extend-ignore' option" in error_msg:
                    patterns.append({
                        'pattern': 'flake8_invalid_error_code',
                        'description': 'Flake8配置中包含无效的错误代码格式',
                        'file_likely': '.flake8',
                        'fix_hint': '移除extend-ignore中的#字符或注释'
                    })
                elif "does not match" in error_msg and "flake8" in error_msg:
                    patterns.append({
                        'pattern': 'flake8_config_format_error',
                        'description': 'Flake8配置格式错误',
                        'file_likely': '.flake8 或 setup.cfg',
                        'fix_hint': '检查配置文件格式和错误代码规范'
                    })
                elif "No such file or directory" in error_msg:
                    patterns.append({
                        'pattern': 'file_not_found',
                        'description': '文件或目录不存在',
                        'fix_hint': '检查文件路径是否正确'
                    })

        return patterns

    def _suggest_error_fixes(self, results: list) -> list:
        """基于错误分析建议修复方案"""
        suggestions = []

        for result in results:
            if not result['success']:
                error_msg = result.get('error', '')
                command = result.get('command', '')

                if "Error code '#' supplied to 'extend-ignore' option" in error_msg:
                    suggestions.append({
                        'error': 'Flake8配置错误',
                        'command': command,
                        'suggestion': '在.flake8文件中，extend-ignore选项包含了无效的#字符。需要移除#字符或将其作为注释处理。',
                        'fix_command': "修改.flake8文件，将'extend-ignore = #E203,W503'改为'extend-ignore = E203,W503'",
                        'priority': 'high'
                    })
                elif "flake8" in error_msg.lower() and "config" in error_msg.lower():
                    suggestions.append({
                        'error': 'Flake8配置问题',
                        'command': command,
                        'suggestion': '检查.flake8或setup.cfg文件中的配置格式是否正确',
                        'fix_command': '验证配置文件语法和错误代码格式',
                        'priority': 'medium'
                    })

        return suggestions

    def _format_execution_plan(self, fix_decision) -> str:
        """格式化执行计划"""
        if fix_decision.strategy == FixStrategy.AUTOMATED:
            commands = fix_decision.automated_commands or []
            return f"将执行 {len(commands)} 个自动化命令:\n" + "\n".join(f"- {cmd}" for cmd in commands)
        elif fix_decision.strategy == FixStrategy.AIDER_GUIDED:
            return "将使用Aider AI进行智能代码修复"
        else:
            return "将使用混合策略：先自动化修复，必要时使用Aider补充"


# 全局实例
global_intelligent_fix_coordinator = IntelligentFixCoordinator()
