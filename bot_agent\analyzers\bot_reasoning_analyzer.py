"""
Bot级别推理分析器

职责：
- 使用推理模型（deepseek-r1）进行高级分析和决策
- 生成执行计划，但不直接执行
- 与Aider执行层分离，保持架构清晰

架构原则：
Bot推理层 → 生成执行计划 → Aider执行层
"""

import logging
import os
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ReasoningResult:
    """推理分析结果"""
    success: bool
    analysis: str
    execution_plan: Dict[str, Any]
    confidence: float
    reasoning_steps: List[str]
    error_message: Optional[str] = None


class BotReasoningAnalyzer:
    """
    Bot级别推理分析器

    专门负责高级推理和决策，不涉及具体执行
    """

    def __init__(self):
        """初始化推理分析器"""
        from bot_agent.config.model_config import ModelConfig
        self.model_name = ModelConfig.get_openrouter_analysis_model()
        logger.info(f"BotReasoningAnalyzer initialized with model: {self.model_name}")

    async def analyze_job_failure(self, job_log: str, job_info: Dict[str, Any],
                                project_path: str, session_id: str = None) -> ReasoningResult:
        """
        分析作业失败并生成执行计划

        Args:
            job_log: 作业日志
            job_info: 作业信息
            project_path: 项目路径
            session_id: 会话ID，用于记录真实的AI调用

        Returns:
            ReasoningResult: 推理分析结果
        """
        try:
            logger.info("🧠 开始Bot级别推理分析...")
            import time

            # 构建推理提示
            reasoning_prompt = self._build_reasoning_prompt(job_log, job_info, project_path)

            # 记录开始时间
            start_time = time.time()

            # 调用推理模型
            reasoning_response = await self._call_reasoning_model(reasoning_prompt)

            # 计算执行时长
            duration = time.time() - start_time

            # 如果提供了session_id，记录真实的AI调用到会话日志
            if session_id:
                from bot_agent.utils.conversation_logger import global_conversation_logger, ConversationStatus

                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_name="Bot推理分析 - 真实AI调用",
                    prompt=reasoning_prompt,  # 真实的AI提示词
                    response=reasoning_response,  # 真实的AI响应
                    model_name=self.model_name,  # 真实的模型名称
                    duration=duration,
                    status=ConversationStatus.SUCCESS
                )
                logger.info(f"✅ 已记录真实AI调用到会话日志，时长: {duration:.2f}秒")

            # 解析推理结果
            analysis_result = self._parse_reasoning_response(reasoning_response)

            # 生成执行计划
            execution_plan = self._generate_execution_plan(analysis_result, job_info, project_path)

            return ReasoningResult(
                success=True,
                analysis=analysis_result.get('analysis', ''),
                execution_plan=execution_plan,
                confidence=analysis_result.get('confidence', 0.8),
                reasoning_steps=analysis_result.get('reasoning_steps', []),
            )

        except Exception as e:
            logger.error(f"推理分析失败: {e}")

            # 如果提供了session_id，记录失败的AI调用
            if session_id:
                from bot_agent.utils.conversation_logger import global_conversation_logger, ConversationStatus

                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_name="Bot推理分析 - AI调用失败",
                    prompt=reasoning_prompt if 'reasoning_prompt' in locals() else "提示词构建失败",
                    response=f"AI调用失败: {str(e)}",
                    model_name=self.model_name,
                    duration=0.0,
                    status=ConversationStatus.FAILED,
                    error_message=str(e)
                )

            return ReasoningResult(
                success=False,
                analysis=f"推理分析失败: {str(e)}",
                execution_plan={},
                confidence=0.0,
                reasoning_steps=[],
                error_message=str(e)
            )

    def _build_reasoning_prompt(self, job_log: str, job_info: Dict[str, Any],
                              project_path: str) -> str:
        """构建推理提示"""
        return f"""
## 🧠 作业失败深度推理分析

你是一个资深的DevOps工程师和问题解决专家。请对以下CI/CD作业失败进行深度推理分析。

### 📋 作业信息
- 作业ID: {job_info.get('id', 'unknown')}
- 作业名称: {job_info.get('name', 'unknown')}
- 作业状态: {job_info.get('status', 'unknown')}
- 项目路径: {project_path}

### 📝 作业日志（最后1000字符）
```
{job_log[-1000:] if len(job_log) > 1000 else job_log}
```

### 🎯 请进行深度推理分析

请按以下步骤进行推理：

1. **错误识别**: 从日志中识别具体的错误信息
2. **根因分析**: 分析错误的根本原因
3. **影响评估**: 评估错误的影响范围
4. **解决策略**: 制定解决策略和优先级
5. **执行计划**: 生成具体的修复步骤

请以JSON格式返回分析结果：
```json
{{
    "analysis": "详细的分析结果",
    "errors_identified": ["错误1", "错误2"],
    "root_causes": ["根因1", "根因2"],
    "impact_assessment": "影响评估",
    "solution_strategy": "解决策略",
    "confidence": 0.9,
    "reasoning_steps": ["推理步骤1", "推理步骤2"],
    "execution_priority": "high|medium|low"
}}
```
"""

    async def _call_reasoning_model(self, prompt: str) -> str:
        """调用推理模型"""
        try:
            # 直接调用OpenRouter API，避免使用有问题的AITaskAnalyzer
            import openai
            import os

            # 获取API配置
            api_key = os.getenv("OPENROUTER_API_KEY")
            if not api_key:
                raise Exception("OPENROUTER_API_KEY not found")

            # 创建OpenAI客户端
            client = openai.OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=api_key
            )

            logger.info(f"🧠 调用推理模型: {self.model_name}")

            # 调用推理模型
            response = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个资深的DevOps工程师和问题解决专家，专门负责分析CI/CD作业失败并提供解决方案。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.7,
                max_tokens=4000
            )

            ai_response = response.choices[0].message.content.strip()
            logger.info(f"🧠 推理模型响应长度: {len(ai_response)} 字符")

            return ai_response

        except Exception as e:
            logger.error(f"调用推理模型失败: {e}")
            raise

    def _parse_reasoning_response(self, response: str) -> Dict[str, Any]:
        """解析推理响应"""
        try:
            # 尝试解析JSON
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
            else:
                json_content = response.strip()

            return json.loads(json_content)

        except Exception as e:
            logger.warning(f"解析推理响应失败，使用文本格式: {e}")
            return {
                'analysis': response,
                'errors_identified': [],
                'root_causes': [],
                'confidence': 0.7,
                'reasoning_steps': ['推理分析完成'],
                'execution_priority': 'medium'
            }

    def _generate_execution_plan(self, analysis_result: Dict[str, Any],
                                job_info: Dict[str, Any], project_path: str) -> Dict[str, Any]:
        """生成执行计划"""
        errors = analysis_result.get('errors_identified', [])
        priority = analysis_result.get('execution_priority', 'medium')

        execution_plan = {
            'type': 'job_failure_fix',
            'priority': priority,
            'project_path': project_path,
            'job_info': job_info,
            'errors_to_fix': errors,
            'execution_steps': [],
            'tools_required': ['aider', 'git', 'file_operations'],
            'estimated_time': self._estimate_execution_time(errors),
            'success_criteria': self._define_success_criteria(job_info)
        }

        # 为每个错误生成执行步骤
        for i, error in enumerate(errors[:5]):  # 限制最多5个错误
            step = {
                'step_id': i + 1,
                'description': f"修复错误: {error}",
                'tool': 'aider',
                'action': 'code_fix',
                'target': error,
                'validation': f"验证{error}是否已修复"
            }
            execution_plan['execution_steps'].append(step)

        return execution_plan

    def _estimate_execution_time(self, errors: List[str]) -> int:
        """估算执行时间（秒）"""
        base_time = 30  # 基础时间
        error_time = len(errors) * 60  # 每个错误60秒
        return min(base_time + error_time, 600)  # 最多10分钟

    def _define_success_criteria(self, job_info: Dict[str, Any]) -> List[str]:
        """定义成功标准"""
        job_name = job_info.get('name', 'unknown')

        if 'lint' in job_name.lower():
            return ['代码通过lint检查', '无语法错误', '符合代码规范']
        elif 'test' in job_name.lower():
            return ['所有测试通过', '无测试失败', '覆盖率达标']
        elif 'build' in job_name.lower():
            return ['构建成功', '无编译错误', '依赖正确安装']
        else:
            return ['作业执行成功', '无错误输出', '符合预期结果']


# 全局实例
global_bot_reasoning_analyzer = BotReasoningAnalyzer()
