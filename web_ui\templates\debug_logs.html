{% extends "base.html" %}

{% block title %}调试日志监控 - Aider行为分析系统{% endblock %}

{% block extra_css %}
<style>
        .debug-log-entry {
            border-left: 4px solid #dee2e6;
            margin-bottom: 8px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 0 4px 4px 0;
        }
        .debug-log-entry.trace { border-left-color: #6c757d; }
        .debug-log-entry.debug { border-left-color: #0dcaf0; }
        .debug-log-entry.info { border-left-color: #0d6efd; }
        .debug-log-entry.warning { border-left-color: #ffc107; }
        .debug-log-entry.error { border-left-color: #dc3545; }
        .debug-log-entry.critical { border-left-color: #6f42c1; }

        .log-timestamp {
            font-size: 0.85em;
            color: #6c757d;
        }

        .log-component {
            font-weight: 600;
            color: #495057;
        }

        .log-category {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.75em;
            font-weight: 500;
            text-transform: uppercase;
        }

        .category-router { background: #e3f2fd; color: #1976d2; }
        .category-coordinator { background: #f3e5f5; color: #7b1fa2; }
        .category-aider { background: #e8f5e8; color: #388e3c; }
        .category-command { background: #fff3e0; color: #f57c00; }
        .category-api { background: #fce4ec; color: #c2185b; }
        .category-file_operation { background: #f1f8e9; color: #689f38; }
        .category-system { background: #efebe9; color: #5d4037; }

        .log-details {
            margin-top: 8px;
            padding: 8px;
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
        }

        .session-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 16px;
        }

        .session-header {
            background: #f8f9fa;
            padding: 12px 16px;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
        }

        .session-logs {
            max-height: 600px;
            overflow-y: auto;
            padding: 12px;
        }

        /* 调试日志特有样式 - 其他样式继承自base.html */

        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-bug text-primary"></i>
        调试日志监控
    </h1>
    <p class="text-muted mb-0">实时监控系统执行过程中的详细调试信息</p>
</div>
<!-- 自动刷新控制 -->
<div class="auto-refresh">
    <div class="form-check form-switch">
        <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
        <label class="form-check-label" for="autoRefresh">
            <i class="fas fa-sync-alt"></i> 自动刷新
        </label>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="metric-value" id="activeSessions">-</div>
            <div class="metric-label">活跃会话</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
            <div class="metric-value" id="recentLogs">-</div>
            <div class="metric-label">最近10分钟</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
            <div class="metric-value" id="errorLogs">-</div>
            <div class="metric-label">错误日志</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="metric-value" id="systemStatus">正常</div>
            <div class="metric-label">系统状态</div>
        </div>
    </div>
</div>

<!-- 过滤控制 -->
<div class="search-filters">
    <div class="row">
                <div class="col-md-3">
                    <label class="form-label">会话ID</label>
                    <select class="form-select" id="sessionFilter">
                        <option value="">所有会话</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">分类</label>
                    <select class="form-select" id="categoryFilter">
                        <option value="">所有分类</option>
                        <option value="router">路由器</option>
                        <option value="coordinator">协调器</option>
                        <option value="aider">Aider</option>
                        <option value="command">命令执行</option>
                        <option value="api">API调用</option>
                        <option value="file_operation">文件操作</option>
                        <option value="system">系统</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">级别</label>
                    <select class="form-select" id="levelFilter">
                        <option value="">所有级别</option>
                        <option value="trace">跟踪</option>
                        <option value="debug">调试</option>
                        <option value="info">信息</option>
                        <option value="warning">警告</option>
                        <option value="error">错误</option>
                        <option value="critical">严重</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">组件</label>
                    <input type="text" class="form-control" id="componentFilter" placeholder="组件名称">
                </div>
                <div class="col-md-2">
                    <label class="form-label">限制条数</label>
                    <select class="form-select" id="limitFilter">
                        <option value="50">50条</option>
                        <option value="100" selected>100条</option>
                        <option value="200">200条</option>
                        <option value="500">500条</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary d-block w-100" onclick="refreshLogs()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-12">
                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="clearLogs()">
                        <i class="fas fa-trash"></i> 清空日志
                    </button>
                    <button class="btn btn-outline-info btn-sm me-2" onclick="exportLogs()">
                        <i class="fas fa-download"></i> 导出日志
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="showSessionSummary()">
                        <i class="fas fa-chart-bar"></i> 会话摘要
                    </button>
                </div>
    </div>
</div>

<!-- 日志显示区域 -->
<div id="logsContainer">
    <div class="text-center py-5">
        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
        <p class="text-muted mt-2">加载调试日志中...</p>
    </div>
</div>

<!-- 会话摘要模态框 -->
<div class="modal fade" id="sessionSummaryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">会话日志摘要</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="sessionSummaryContent">
                <!-- 摘要内容将在这里动态加载 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
        let autoRefreshInterval;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshLogs();
            loadActiveSessions();
            startAutoRefresh();
        });

        // 自动刷新控制
        document.getElementById('autoRefresh').addEventListener('change', function() {
            if (this.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });

        function startAutoRefresh() {
            stopAutoRefresh();
            autoRefreshInterval = setInterval(refreshLogs, 5000); // 每5秒刷新
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        // 刷新日志
        async function refreshLogs() {
            try {
                const sessionId = document.getElementById('sessionFilter').value;
                const category = document.getElementById('categoryFilter').value;
                const level = document.getElementById('levelFilter').value;
                const component = document.getElementById('componentFilter').value;
                const limit = document.getElementById('limitFilter').value;

                const params = new URLSearchParams();
                if (sessionId) params.append('session_id', sessionId);
                if (category) params.append('category', category);
                if (level) params.append('level', level);
                if (component) params.append('component', component);
                if (limit) params.append('limit', limit);

                const response = await fetch(`/api/debug-logs?${params}`);
                const data = await response.json();

                displayLogs(data.logs);
                updateStats(data.stats);

            } catch (error) {
                console.error('刷新日志失败:', error);
                document.getElementById('logsContainer').innerHTML =
                    '<div class="alert alert-danger">加载日志失败: ' + error.message + '</div>';
            }
        }

        // 显示日志
        function displayLogs(logs) {
            const container = document.getElementById('logsContainer');

            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="alert alert-info">没有找到匹配的日志记录</div>';
                return;
            }

            // 按会话分组
            const sessionGroups = {};
            logs.forEach(log => {
                if (!sessionGroups[log.session_id]) {
                    sessionGroups[log.session_id] = [];
                }
                sessionGroups[log.session_id].push(log);
            });

            let html = '';
            Object.keys(sessionGroups).forEach(sessionId => {
                const sessionLogs = sessionGroups[sessionId];
                html += `
                    <div class="session-card">
                        <div class="session-header">
                            <h6 class="mb-0">
                                <i class="fas fa-user-circle me-2"></i>会话: ${sessionId}
                                <span class="badge bg-secondary ms-2">${sessionLogs.length} 条日志</span>
                            </h6>
                        </div>
                        <div class="session-logs">
                            ${sessionLogs.map(log => formatLogEntry(log)).join('')}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 格式化日志条目
        function formatLogEntry(log) {
            const categoryClass = `category-${log.category}`;
            const levelClass = log.level;

            let detailsHtml = '';
            if (log.details && Object.keys(log.details).length > 0) {
                detailsHtml = `
                    <div class="log-details">
                        <strong>详细信息:</strong><br>
                        ${JSON.stringify(log.details, null, 2)}
                    </div>
                `;
            }

            return `
                <div class="debug-log-entry ${levelClass}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <span class="log-category ${categoryClass}">${log.category}</span>
                                <span class="log-component ms-2">${log.component}</span>
                                <span class="log-timestamp ms-auto">${log.formatted_time}</span>
                            </div>
                            <div class="log-message">${log.message}</div>
                            ${detailsHtml}
                        </div>
                    </div>
                </div>
            `;
        }

        // 更新统计信息
        function updateStats(stats) {
            if (stats) {
                document.getElementById('activeSessions').textContent = stats.active_sessions || 0;
                document.getElementById('recentLogs').textContent = stats.recent_logs || 0;
                document.getElementById('errorLogs').textContent = stats.error_logs || 0;
            }
        }

        // 加载活跃会话
        async function loadActiveSessions() {
            try {
                const response = await fetch('/api/debug-logs/sessions');
                const sessions = await response.json();

                const select = document.getElementById('sessionFilter');
                select.innerHTML = '<option value="">所有会话</option>';

                sessions.forEach(session => {
                    const option = document.createElement('option');
                    option.value = session;
                    option.textContent = session;
                    select.appendChild(option);
                });

            } catch (error) {
                console.error('加载会话列表失败:', error);
            }
        }

        // 清空日志
        async function clearLogs() {
            if (!confirm('确定要清空所有调试日志吗？')) return;

            try {
                const response = await fetch('/api/debug-logs/clear', { method: 'POST' });
                if (response.ok) {
                    refreshLogs();
                    alert('日志已清空');
                } else {
                    alert('清空日志失败');
                }
            } catch (error) {
                console.error('清空日志失败:', error);
                alert('清空日志失败: ' + error.message);
            }
        }

        // 导出日志
        async function exportLogs() {
            try {
                const sessionId = document.getElementById('sessionFilter').value;
                const params = sessionId ? `?session_id=${sessionId}` : '';

                const response = await fetch(`/api/debug-logs/export${params}`);
                const blob = await response.blob();

                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `debug_logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

            } catch (error) {
                console.error('导出日志失败:', error);
                alert('导出日志失败: ' + error.message);
            }
        }

        // 显示会话摘要
        async function showSessionSummary() {
            const sessionId = document.getElementById('sessionFilter').value;
            if (!sessionId) {
                alert('请先选择一个会话');
                return;
            }

            try {
                const response = await fetch(`/api/debug-logs/summary?session_id=${sessionId}`);
                const summary = await response.json();

                const content = document.getElementById('sessionSummaryContent');
                content.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <ul class="list-unstyled">
                                <li><strong>会话ID:</strong> ${summary.session_id}</li>
                                <li><strong>总日志数:</strong> ${summary.total_logs}</li>
                                <li><strong>持续时间:</strong> ${summary.time_range ? (summary.time_range.duration / 60).toFixed(1) + ' 分钟' : 'N/A'}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>分类统计</h6>
                            <ul class="list-unstyled">
                                ${Object.entries(summary.categories || {}).map(([cat, count]) =>
                                    `<li><strong>${cat}:</strong> ${count}</li>`
                                ).join('')}
                            </ul>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>级别统计</h6>
                            <ul class="list-unstyled">
                                ${Object.entries(summary.levels || {}).map(([level, count]) =>
                                    `<li><strong>${level}:</strong> ${count}</li>`
                                ).join('')}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>组件统计</h6>
                            <ul class="list-unstyled">
                                ${Object.entries(summary.components || {}).map(([comp, count]) =>
                                    `<li><strong>${comp}:</strong> ${count}</li>`
                                ).join('')}
                            </ul>
                        </div>
                    </div>
                `;

                new bootstrap.Modal(document.getElementById('sessionSummaryModal')).show();

            } catch (error) {
                console.error('获取会话摘要失败:', error);
                alert('获取会话摘要失败: ' + error.message);
            }
        }
</script>
{% endblock %}
