2025-05-31 11:40:45,919 - bot_agent.aider_extensions.aider_monitor - ERROR - aider_monitor.py:62 - log_operation - [AIDER_MONITOR]   错误: MonitoredIO.confirm_ask() got an unexpected keyword argument 'subject'
2025-05-31 11:40:45,922 - bot_agent.aider_extensions.aider_monitor - ERROR - aider_monitor.py:332 - operation_callback - 🚨 Coder执行失败: MonitoredIO.confirm_ask() got an unexpected keyword argument 'subject'
2025-05-31 11:40:45,923 - bot_agent.executors.aider_executor - ERROR - aider_executor.py:337 - _execute_step - 步骤 1 执行失败: MonitoredIO.confirm_ask() got an unexpected keyword argument 'subject'
2025-05-31 11:40:45,925 - bot_agent.executors.aider_executor - ERROR - aider_executor.py:340 - _execute_step - 详细错误: Traceback (most recent call last):
  File "E:\Projects\aider-plus\bot_agent\executors\aider_executor.py", line 280, in _execute_step
    response = aider_coder.run(execution_command)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\bot_agent\aider_extensions\aider_monitor.py", line 256, in run
    result = self.original_coder.run(with_message=with_message, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\aider\coders\base_coder.py", line 849, in run
    self.run_one(with_message, preproc)
  File "E:\Projects\aider-plus\aider\coders\base_coder.py", line 903, in run_one
    list(self.send_message(message))
  File "E:\Projects\aider-plus\aider\coders\base_coder.py", line 1456, in send_message
    add_rel_files_message = self.check_for_file_mentions(content)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\aider\coders\base_coder.py", line 1667, in check_for_file_mentions
    if self.io.confirm_ask(
       ^^^^^^^^^^^^^^^^^^^^
TypeError: MonitoredIO.confirm_ask() got an unexpected keyword argument 'subject'

2025-05-31 11:40:51,588 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-31 11:40:51,588 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

