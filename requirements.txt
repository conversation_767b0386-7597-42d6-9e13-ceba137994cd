# Core dependencies
fastapi>=0.95.0
uvicorn>=0.21.0
python-consul>=1.1.0
requests>=2.28.0
python-multipart>=0.0.6
pydantic>=1.10.0
python-dotenv>=1.0.0
PyJWT>=2.6.0
httpx>=0.24.0
jinja2>=3.0.0

# AI and code processing
openai>=1.0.0
litellm>=1.0.0
tiktoken>=0.0.5
tokenizers>=0.13.0
huggingface-hub>=0.13.0
json5>=0.9.0
importlib_resources>=5.0.0
prompt_toolkit>=3.0.0
pillow>=9.0.0
mixpanel>=4.10.0
posthog>=2.0.0

# Tools and utilities
rich>=10.0.0
tqdm>=4.0.0
pexpect>=4.0.0
gitpython>=3.0.0
watchfiles>=0.0.0
pathspec>=0.0.0
jsonschema>=4.0.0
networkx>=2.0
diskcache>=5.0.0
posthog>=2.0.0
beautifulsoup4>=4.0.0
psutil>=5.0.0
pypandoc>=1.5
toml>=0.10.0

# Aider dependencies
configargparse>=1.5.0
pyperclip>=1.8.0
pillow>=9.0.0
prompt_toolkit>=3.0.0
litellm>=0.1.0
grep_ast>=0.3.0
diff-match-patch>=20200713
scipy<1.14
numpy>=1.21.0
backoff>=2.0.0
socksio>=1.0.0
importlib-metadata<8.0.0

# Testing and code quality
pytest>=7.0.0
pytest-cov>=4.0.0
flake8>=6.0.0
autopep8>=2.0.0
isort>=5.0.0

# Audio processing (if needed)
sounddevice>=0.4.0
soundfile>=0.10.0
pydub>=0.25.0

# This file was autogenerated by uv via the following command:
#    uv pip compile --no-strip-extras --constraint=requirements/common-constraints.txt --output-file=requirements/requirements-playwright.txt requirements/requirements-playwright.in
greenlet==3.1.1
    # via
    #   -c requirements/common-constraints.txt
    #   playwright
playwright==1.51.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-playwright.in
pyee==12.1.1
    # via
    #   -c requirements/common-constraints.txt
    #   playwright
typing-extensions==4.13.2
    # via
    #   -c requirements/common-constraints.txt
    #   pyee
