{"job:lint|types:flake8_config_error,generic_job_failure|keywords:extend-ignore": {"error_signature": "job:lint|types:flake8_config_error,generic_job_failure|keywords:extend-ignore", "fix_strategy": "automated_fallback_to_aider", "success_count": 11, "failure_count": 0, "avg_execution_time": 13.855146603150802, "last_used": 1748657656.6561089, "complexity_score": 0.2, "commands_used": [], "context_factors": {"project_type": "unknown", "error_count": 3, "has_config_files": []}}, "job:test|types:generic_job_failure,import_error|keywords:": {"error_signature": "job:test|types:generic_job_failure,import_error|keywords:", "fix_strategy": "aider_guided", "success_count": 3, "failure_count": 0, "avg_execution_time": 2.2985899448394775, "last_used": 1748657618.4241574, "complexity_score": 0.8, "commands_used": [], "context_factors": {"project_type": "unknown", "error_count": 24, "has_config_files": []}}, "job:lint|types:flake8_config_error,generic_job_failure,network_timeout_error|keywords:extend-ignore,flake8": {"error_signature": "job:lint|types:flake8_config_error,generic_job_failure,network_timeout_error|keywords:extend-ignore,flake8", "fix_strategy": "automated_fallback_to_aider", "success_count": 1, "failure_count": 0, "avg_execution_time": 36.42753267288208, "last_used": 1748612490.022301, "complexity_score": 0.2, "commands_used": [], "context_factors": {"project_type": "unknown", "error_count": 4, "has_config_files": []}}}