"""
实时反馈系统 - 提供修复过程的实时反馈和交互

核心功能：
1. 实时修复进度反馈
2. 用户交互确认机制
3. 修复过程可视化
4. 异常情况的人工介入
"""

import logging
import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)

class FeedbackType(Enum):
    """反馈类型"""
    PROGRESS = "progress"           # 进度更新
    SUCCESS = "success"            # 成功消息
    WARNING = "warning"            # 警告消息
    ERROR = "error"               # 错误消息
    QUESTION = "question"         # 需要用户确认
    INFO = "info"                 # 信息提示

class InteractionType(Enum):
    """交互类型"""
    CONFIRMATION = "confirmation"   # 确认操作
    CHOICE = "choice"              # 选择选项
    INPUT = "input"                # 输入内容
    APPROVAL = "approval"          # 批准操作

@dataclass
class FeedbackMessage:
    """反馈消息"""
    type: FeedbackType
    title: str
    message: str
    timestamp: float
    session_id: str
    step_id: Optional[str] = None
    progress: Optional[float] = None  # 0.0-1.0
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class InteractionRequest:
    """交互请求"""
    type: InteractionType
    title: str
    message: str
    options: Optional[List[str]] = None
    default_value: Optional[str] = None
    timeout: Optional[int] = None  # 超时时间（秒）
    session_id: str = ""
    request_id: str = ""

class RealTimeFeedbackSystem:
    """实时反馈系统"""
    
    def __init__(self):
        self.feedback_callbacks = []
        self.interaction_callbacks = []
        self.active_sessions = {}
        self.pending_interactions = {}
        self.feedback_history = {}
    
    def register_feedback_callback(self, callback: Callable[[FeedbackMessage], None]):
        """注册反馈回调"""
        self.feedback_callbacks.append(callback)
    
    def register_interaction_callback(self, callback: Callable[[InteractionRequest], Any]):
        """注册交互回调"""
        self.interaction_callbacks.append(callback)
    
    async def send_feedback(self, 
                          session_id: str,
                          feedback_type: FeedbackType,
                          title: str,
                          message: str,
                          step_id: Optional[str] = None,
                          progress: Optional[float] = None,
                          metadata: Optional[Dict[str, Any]] = None):
        """发送反馈消息"""
        try:
            feedback = FeedbackMessage(
                type=feedback_type,
                title=title,
                message=message,
                timestamp=time.time(),
                session_id=session_id,
                step_id=step_id,
                progress=progress,
                metadata=metadata
            )
            
            # 记录反馈历史
            if session_id not in self.feedback_history:
                self.feedback_history[session_id] = []
            self.feedback_history[session_id].append(feedback)
            
            # 调用所有反馈回调
            for callback in self.feedback_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(feedback)
                    else:
                        callback(feedback)
                except Exception as e:
                    logger.error(f"反馈回调执行失败: {e}")
            
            logger.info(f"📢 发送反馈: {session_id} - {title}")
            
        except Exception as e:
            logger.error(f"发送反馈失败: {e}")
    
    async def request_interaction(self, 
                                interaction_type: InteractionType,
                                title: str,
                                message: str,
                                session_id: str,
                                options: Optional[List[str]] = None,
                                default_value: Optional[str] = None,
                                timeout: int = 300) -> Optional[str]:
        """请求用户交互"""
        try:
            request_id = f"{session_id}_{int(time.time() * 1000)}"
            
            request = InteractionRequest(
                type=interaction_type,
                title=title,
                message=message,
                options=options,
                default_value=default_value,
                timeout=timeout,
                session_id=session_id,
                request_id=request_id
            )
            
            # 记录待处理的交互
            self.pending_interactions[request_id] = {
                'request': request,
                'response': None,
                'completed': False,
                'start_time': time.time()
            }
            
            # 发送交互请求反馈
            await self.send_feedback(
                session_id=session_id,
                feedback_type=FeedbackType.QUESTION,
                title=f"需要确认: {title}",
                message=message,
                metadata={
                    'interaction_type': interaction_type.value,
                    'request_id': request_id,
                    'options': options,
                    'timeout': timeout
                }
            )
            
            # 调用交互回调
            response = None
            for callback in self.interaction_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        response = await callback(request)
                    else:
                        response = callback(request)
                    
                    if response is not None:
                        break
                except Exception as e:
                    logger.error(f"交互回调执行失败: {e}")
            
            # 等待响应或超时
            if response is None:
                response = await self._wait_for_interaction_response(request_id, timeout)
            
            # 清理待处理的交互
            if request_id in self.pending_interactions:
                del self.pending_interactions[request_id]
            
            # 发送响应反馈
            if response is not None:
                await self.send_feedback(
                    session_id=session_id,
                    feedback_type=FeedbackType.INFO,
                    title="用户响应",
                    message=f"用户选择: {response}",
                    metadata={'request_id': request_id, 'response': response}
                )
            else:
                await self.send_feedback(
                    session_id=session_id,
                    feedback_type=FeedbackType.WARNING,
                    title="交互超时",
                    message=f"用户未在{timeout}秒内响应，使用默认值: {default_value}",
                    metadata={'request_id': request_id, 'timeout': True}
                )
                response = default_value
            
            return response
            
        except Exception as e:
            logger.error(f"请求用户交互失败: {e}")
            return default_value
    
    async def _wait_for_interaction_response(self, request_id: str, timeout: int) -> Optional[str]:
        """等待交互响应"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if request_id in self.pending_interactions:
                interaction = self.pending_interactions[request_id]
                if interaction['completed']:
                    return interaction['response']
            
            await asyncio.sleep(0.5)  # 每0.5秒检查一次
        
        return None
    
    def respond_to_interaction(self, request_id: str, response: str) -> bool:
        """响应交互请求"""
        try:
            if request_id in self.pending_interactions:
                self.pending_interactions[request_id]['response'] = response
                self.pending_interactions[request_id]['completed'] = True
                logger.info(f"✅ 收到交互响应: {request_id} -> {response}")
                return True
            else:
                logger.warning(f"未找到交互请求: {request_id}")
                return False
                
        except Exception as e:
            logger.error(f"响应交互请求失败: {e}")
            return False
    
    async def start_fix_session(self, session_id: str, total_steps: int) -> 'FixProgressTracker':
        """开始修复会话"""
        tracker = FixProgressTracker(self, session_id, total_steps)
        self.active_sessions[session_id] = tracker
        
        await self.send_feedback(
            session_id=session_id,
            feedback_type=FeedbackType.INFO,
            title="开始修复",
            message=f"开始智能修复流程，共{total_steps}个步骤",
            progress=0.0,
            metadata={'total_steps': total_steps}
        )
        
        return tracker
    
    def end_fix_session(self, session_id: str):
        """结束修复会话"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
        
        # 保留反馈历史一段时间
        # 可以考虑定期清理旧的历史记录
    
    def get_session_feedback(self, session_id: str) -> List[FeedbackMessage]:
        """获取会话反馈历史"""
        return self.feedback_history.get(session_id, [])
    
    def get_pending_interactions(self) -> Dict[str, Dict[str, Any]]:
        """获取待处理的交互"""
        return self.pending_interactions.copy()

class FixProgressTracker:
    """修复进度跟踪器"""
    
    def __init__(self, feedback_system: RealTimeFeedbackSystem, session_id: str, total_steps: int):
        self.feedback_system = feedback_system
        self.session_id = session_id
        self.total_steps = total_steps
        self.current_step = 0
        self.completed_steps = []
        self.failed_steps = []
    
    async def start_step(self, step_id: str, step_name: str, description: str = ""):
        """开始执行步骤"""
        self.current_step += 1
        progress = self.current_step / self.total_steps
        
        await self.feedback_system.send_feedback(
            session_id=self.session_id,
            feedback_type=FeedbackType.PROGRESS,
            title=f"步骤 {self.current_step}/{self.total_steps}: {step_name}",
            message=description or f"正在执行: {step_name}",
            step_id=step_id,
            progress=progress,
            metadata={
                'step_number': self.current_step,
                'total_steps': self.total_steps,
                'step_id': step_id
            }
        )
    
    async def complete_step(self, step_id: str, result: str = ""):
        """完成步骤"""
        self.completed_steps.append(step_id)
        
        await self.feedback_system.send_feedback(
            session_id=self.session_id,
            feedback_type=FeedbackType.SUCCESS,
            title=f"步骤完成: {step_id}",
            message=result or "步骤执行成功",
            step_id=step_id,
            metadata={'completed': True, 'result': result}
        )
    
    async def fail_step(self, step_id: str, error: str):
        """步骤失败"""
        self.failed_steps.append(step_id)
        
        await self.feedback_system.send_feedback(
            session_id=self.session_id,
            feedback_type=FeedbackType.ERROR,
            title=f"步骤失败: {step_id}",
            message=error,
            step_id=step_id,
            metadata={'failed': True, 'error': error}
        )
    
    async def request_confirmation(self, title: str, message: str, default: str = "yes") -> bool:
        """请求确认"""
        response = await self.feedback_system.request_interaction(
            interaction_type=InteractionType.CONFIRMATION,
            title=title,
            message=message,
            session_id=self.session_id,
            options=["yes", "no"],
            default_value=default,
            timeout=60
        )
        
        return response and response.lower() in ['yes', 'y', '是', '确认']
    
    async def request_choice(self, title: str, message: str, options: List[str], default: str = None) -> str:
        """请求选择"""
        return await self.feedback_system.request_interaction(
            interaction_type=InteractionType.CHOICE,
            title=title,
            message=message,
            session_id=self.session_id,
            options=options,
            default_value=default or options[0],
            timeout=120
        )
    
    def get_progress(self) -> Dict[str, Any]:
        """获取进度信息"""
        return {
            'current_step': self.current_step,
            'total_steps': self.total_steps,
            'progress': self.current_step / self.total_steps,
            'completed_steps': len(self.completed_steps),
            'failed_steps': len(self.failed_steps),
            'success_rate': len(self.completed_steps) / max(self.current_step, 1)
        }


# 全局实时反馈系统实例
global_real_time_feedback_system = RealTimeFeedbackSystem()

# 默认的控制台反馈回调
async def console_feedback_callback(feedback: FeedbackMessage):
    """控制台反馈回调"""
    emoji_map = {
        FeedbackType.PROGRESS: "🔄",
        FeedbackType.SUCCESS: "✅",
        FeedbackType.WARNING: "⚠️",
        FeedbackType.ERROR: "❌",
        FeedbackType.QUESTION: "❓",
        FeedbackType.INFO: "ℹ️"
    }
    
    emoji = emoji_map.get(feedback.type, "📢")
    progress_str = f" ({feedback.progress:.1%})" if feedback.progress is not None else ""
    
    print(f"{emoji} [{feedback.session_id}] {feedback.title}{progress_str}")
    print(f"   {feedback.message}")

# 注册默认回调
global_real_time_feedback_system.register_feedback_callback(console_feedback_callback)
