"""
增强的Aider集成 - 深度利用Aider的高级功能

核心功能：
1. 利用Aider RepoMap进行代码理解
2. 多文件协调修复
3. 增量编辑和上下文保持
4. 智能代码重构和优化
"""

import logging
import os
import tempfile
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class AiderSession:
    """Aider会话"""
    session_id: str
    project_path: str
    coder: Any = None
    repo_map: Dict[str, Any] = None
    context_files: List[str] = None
    conversation_history: List[Dict[str, str]] = None

class EnhancedAiderIntegration:
    """增强的Aider集成"""
    
    def __init__(self):
        self.active_sessions = {}
        self.repo_maps_cache = {}
    
    async def create_enhanced_session(self, 
                                    project_path: str, 
                                    session_id: str,
                                    error_context: Dict[str, Any]) -> AiderSession:
        """创建增强的Aider会话"""
        try:
            from aider.io import InputOutput
            from aider.models import Model
            from aider.coders import Coder
            from aider.repo import GitRepo
            
            # 1. 初始化Aider组件
            io = InputOutput(yes=True, chat_history_file=None)
            model = Model("deepseek/deepseek-coder")
            
            # 2. 创建或获取Git仓库
            repo = GitRepo(project_path, io)
            
            # 3. 生成RepoMap
            repo_map = await self._generate_enhanced_repo_map(project_path, error_context)
            
            # 4. 智能选择相关文件
            relevant_files = await self._select_relevant_files(project_path, error_context, repo_map)
            
            # 5. 创建Coder实例
            coder = Coder.create(
                main_model=model,
                edit_format="diff",
                io=io,
                repo=repo,
                fnames=relevant_files,
                use_git=True,
                stream=False
            )
            
            # 6. 创建会话
            session = AiderSession(
                session_id=session_id,
                project_path=project_path,
                coder=coder,
                repo_map=repo_map,
                context_files=relevant_files,
                conversation_history=[]
            )
            
            self.active_sessions[session_id] = session
            logger.info(f"✅ 创建增强Aider会话: {session_id}")
            
            return session
            
        except Exception as e:
            logger.error(f"创建增强Aider会话失败: {e}")
            raise
    
    async def _generate_enhanced_repo_map(self, 
                                        project_path: str, 
                                        error_context: Dict[str, Any]) -> Dict[str, Any]:
        """生成增强的代码仓库地图"""
        try:
            # 检查缓存
            cache_key = f"{project_path}_{hash(str(error_context))}"
            if cache_key in self.repo_maps_cache:
                return self.repo_maps_cache[cache_key]
            
            from aider.repomap import RepoMap
            
            # 创建RepoMap实例
            repo_map = RepoMap(
                map_tokens=2048,  # 增加token限制
                root=project_path,
                main_model=None,
                io=None
            )
            
            # 获取错误相关的文件
            error_files = self._extract_error_files(error_context)
            
            # 生成针对性的代码地图
            if error_files:
                # 为错误文件生成详细地图
                detailed_map = repo_map.get_repo_map(error_files)
            else:
                # 生成整体项目地图
                detailed_map = repo_map.get_repo_map()
            
            # 分析代码结构
            structure_analysis = await self._analyze_code_structure(project_path, detailed_map)
            
            enhanced_map = {
                'detailed_map': detailed_map,
                'structure_analysis': structure_analysis,
                'error_files': error_files,
                'project_summary': await self._generate_project_summary(project_path)
            }
            
            # 缓存结果
            self.repo_maps_cache[cache_key] = enhanced_map
            
            return enhanced_map
            
        except Exception as e:
            logger.error(f"生成增强RepoMap失败: {e}")
            return {'error': str(e)}
    
    def _extract_error_files(self, error_context: Dict[str, Any]) -> List[str]:
        """从错误上下文中提取相关文件"""
        files = []
        
        try:
            # 从分类错误中提取
            if 'classified_errors' in error_context:
                for error in error_context['classified_errors']:
                    if 'affected_files' in error:
                        files.extend(error['affected_files'])
            
            # 从原始错误中提取
            if 'all_errors' in error_context:
                for error in error_context['all_errors']:
                    error_text = str(error)
                    # 使用正则表达式提取文件路径
                    import re
                    file_patterns = [
                        r'File "([^"]+)"',
                        r'([^\s]+\.py):\d+',
                        r'in file ([^\s]+)',
                    ]
                    
                    for pattern in file_patterns:
                        matches = re.findall(pattern, error_text)
                        files.extend(matches)
            
            # 去重并过滤有效文件
            unique_files = list(set(files))
            valid_files = [f for f in unique_files if f and os.path.exists(f)]
            
            return valid_files
            
        except Exception as e:
            logger.error(f"提取错误文件失败: {e}")
            return []
    
    async def _select_relevant_files(self, 
                                   project_path: str, 
                                   error_context: Dict[str, Any],
                                   repo_map: Dict[str, Any]) -> List[str]:
        """智能选择相关文件"""
        try:
            relevant_files = []
            
            # 1. 添加错误直接涉及的文件
            error_files = self._extract_error_files(error_context)
            relevant_files.extend(error_files)
            
            # 2. 基于错误类型添加相关配置文件
            error_categories = []
            if 'classified_errors' in error_context:
                error_categories = [err.get('category', '') for err in error_context['classified_errors']]
            
            config_files = []
            if 'configuration' in error_categories or 'lint' in error_categories:
                config_files.extend([
                    '.flake8', 'setup.cfg', 'pyproject.toml', 'tox.ini'
                ])
            
            if 'dependency' in error_categories:
                config_files.extend([
                    'requirements.txt', 'requirements-dev.txt', 'setup.py'
                ])
            
            # 添加存在的配置文件
            for config_file in config_files:
                config_path = os.path.join(project_path, config_file)
                if os.path.exists(config_path):
                    relevant_files.append(config_path)
            
            # 3. 基于RepoMap添加相关的代码文件
            if repo_map.get('structure_analysis'):
                structure = repo_map['structure_analysis']
                if 'main_modules' in structure:
                    relevant_files.extend(structure['main_modules'][:3])  # 最多3个主要模块
            
            # 4. 去重并限制文件数量
            unique_files = list(set(relevant_files))
            
            # 限制文件数量以避免上下文过载
            max_files = 10
            if len(unique_files) > max_files:
                # 优先保留错误文件和配置文件
                priority_files = error_files + [f for f in unique_files if any(
                    config in f for config in ['.flake8', 'pyproject.toml', 'requirements.txt']
                )]
                other_files = [f for f in unique_files if f not in priority_files]
                unique_files = priority_files + other_files[:max_files-len(priority_files)]
            
            return unique_files[:max_files]
            
        except Exception as e:
            logger.error(f"选择相关文件失败: {e}")
            return error_files if 'error_files' in locals() else []
    
    async def _analyze_code_structure(self, project_path: str, repo_map: str) -> Dict[str, Any]:
        """分析代码结构"""
        try:
            structure = {
                'main_modules': [],
                'test_files': [],
                'config_files': [],
                'dependencies': []
            }
            
            # 扫描项目文件
            for root, dirs, files in os.walk(project_path):
                # 跳过隐藏目录和虚拟环境
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['venv', '__pycache__']]
                
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, project_path)
                    
                    if file.endswith('.py'):
                        if 'test' in file.lower() or 'test' in root.lower():
                            structure['test_files'].append(rel_path)
                        else:
                            structure['main_modules'].append(rel_path)
                    
                    elif file in ['.flake8', 'pyproject.toml', 'setup.cfg', 'requirements.txt']:
                        structure['config_files'].append(rel_path)
            
            # 按文件大小排序主要模块（大文件通常更重要）
            main_modules_with_size = []
            for module in structure['main_modules']:
                try:
                    size = os.path.getsize(os.path.join(project_path, module))
                    main_modules_with_size.append((module, size))
                except:
                    main_modules_with_size.append((module, 0))
            
            main_modules_with_size.sort(key=lambda x: x[1], reverse=True)
            structure['main_modules'] = [m[0] for m in main_modules_with_size]
            
            return structure
            
        except Exception as e:
            logger.error(f"分析代码结构失败: {e}")
            return {}
    
    async def _generate_project_summary(self, project_path: str) -> str:
        """生成项目摘要"""
        try:
            summary_parts = []
            
            # 检测项目类型
            if os.path.exists(os.path.join(project_path, 'setup.py')):
                summary_parts.append("Python包项目")
            elif os.path.exists(os.path.join(project_path, 'pyproject.toml')):
                summary_parts.append("现代Python项目")
            else:
                summary_parts.append("Python项目")
            
            # 检测框架
            requirements_file = os.path.join(project_path, 'requirements.txt')
            if os.path.exists(requirements_file):
                with open(requirements_file, 'r') as f:
                    content = f.read().lower()
                    if 'django' in content:
                        summary_parts.append("使用Django框架")
                    elif 'flask' in content:
                        summary_parts.append("使用Flask框架")
                    elif 'fastapi' in content:
                        summary_parts.append("使用FastAPI框架")
            
            # 检测测试框架
            if any(os.path.exists(os.path.join(project_path, d)) for d in ['tests', 'test']):
                summary_parts.append("包含测试")
            
            return "，".join(summary_parts) if summary_parts else "Python项目"
            
        except Exception as e:
            logger.error(f"生成项目摘要失败: {e}")
            return "项目"
    
    async def execute_intelligent_fix(self, 
                                    session_id: str,
                                    fix_instructions: str,
                                    error_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行智能修复"""
        try:
            if session_id not in self.active_sessions:
                raise ValueError(f"会话不存在: {session_id}")
            
            session = self.active_sessions[session_id]
            
            # 构建增强的修复指令
            enhanced_instructions = await self._build_enhanced_instructions(
                fix_instructions, error_context, session
            )
            
            # 执行修复
            response = session.coder.run(with_message=enhanced_instructions)
            
            # 记录对话历史
            session.conversation_history.append({
                'type': 'fix_request',
                'instructions': enhanced_instructions,
                'response': response
            })
            
            return {
                'success': True,
                'response': response,
                'files_modified': session.coder.get_inchat_relative_files(),
                'session_id': session_id
            }
            
        except Exception as e:
            logger.error(f"执行智能修复失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'session_id': session_id
            }
    
    async def _build_enhanced_instructions(self, 
                                         base_instructions: str,
                                         error_context: Dict[str, Any],
                                         session: AiderSession) -> str:
        """构建增强的修复指令"""
        try:
            enhanced_parts = [
                "# 🎯 智能修复任务",
                "",
                "## 📋 项目上下文",
                f"项目类型: {session.repo_map.get('project_summary', '未知')}",
                f"相关文件: {', '.join(session.context_files[:5])}",
                "",
                "## 🔍 错误分析",
            ]
            
            # 添加分类错误信息
            if 'classified_errors' in error_context:
                enhanced_parts.append("### 分类错误:")
                for i, error in enumerate(error_context['classified_errors'][:3], 1):
                    enhanced_parts.append(f"{i}. **{error.get('category', '未知')}** (优先级: {error.get('priority', '未知')})")
                    enhanced_parts.append(f"   描述: {error.get('description', '无描述')}")
                    enhanced_parts.append("")
            
            # 添加代码结构信息
            if session.repo_map.get('structure_analysis'):
                structure = session.repo_map['structure_analysis']
                enhanced_parts.extend([
                    "## 📁 代码结构",
                    f"主要模块: {', '.join(structure.get('main_modules', [])[:3])}",
                    f"配置文件: {', '.join(structure.get('config_files', []))}",
                    ""
                ])
            
            # 添加基础修复指令
            enhanced_parts.extend([
                "## 🔧 修复要求",
                base_instructions,
                "",
                "## 📝 修复指导原则",
                "1. 优先修复高优先级错误",
                "2. 保持代码风格一致性",
                "3. 添加必要的注释说明",
                "4. 确保修复不会引入新问题",
                "5. 如果需要修改配置文件，请解释原因",
                "",
                "请用中文详细说明修复思路，然后实施修复。"
            ])
            
            return "\n".join(enhanced_parts)
            
        except Exception as e:
            logger.error(f"构建增强指令失败: {e}")
            return base_instructions
    
    def close_session(self, session_id: str):
        """关闭会话"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            logger.info(f"🔒 关闭Aider会话: {session_id}")
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        return {
            'session_id': session_id,
            'project_path': session.project_path,
            'context_files': session.context_files,
            'conversation_count': len(session.conversation_history),
            'repo_map_available': session.repo_map is not None
        }


# 全局增强Aider集成实例
global_enhanced_aider_integration = EnhancedAiderIntegration()
