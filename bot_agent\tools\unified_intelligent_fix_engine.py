"""
统一智能修复引擎 - 整合所有修复组件的统一入口

核心功能：
1. 统一修复流程管控
2. 集成智能路由、验证、学习机制
3. 提供简洁的API接口
4. 完整的错误处理和回退机制
"""

import logging
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .intelligent_fix_coordinator import global_intelligent_fix_coordinator
from .intelligent_fix_validator import global_intelligent_fix_validator
from .fix_learning_engine import global_fix_learning_engine
from ..utils.conversation_logger import global_conversation_logger, ConversationStatus

logger = logging.getLogger(__name__)

@dataclass
class UnifiedFixResult:
    """统一修复结果"""
    success: bool
    message: str
    strategy_used: str
    complexity: str
    execution_time: float
    validation_result: Optional[Dict[str, Any]] = None
    learning_recorded: bool = False
    detailed_data: Dict[str, Any] = None

class UnifiedIntelligentFixEngine:
    """统一智能修复引擎"""
    
    def __init__(self):
        self.coordinator = global_intelligent_fix_coordinator
        self.validator = global_intelligent_fix_validator
        self.learning_engine = global_fix_learning_engine
    
    async def execute_complete_fix_flow(self, 
                                      error_analysis: Dict[str, Any],
                                      project_path: str,
                                      session_id: str,
                                      job_info: Dict[str, Any] = None) -> UnifiedFixResult:
        """
        执行完整的智能修复流程
        
        Args:
            error_analysis: 错误分析结果
            project_path: 项目路径
            session_id: 会话ID
            job_info: 作业信息
            
        Returns:
            UnifiedFixResult: 统一修复结果
        """
        try:
            logger.info("🚀 启动统一智能修复引擎...")
            start_time = time.time()
            
            # 第1步：检查学习引擎是否有推荐策略
            recommended_strategy = self.learning_engine.get_recommended_strategy(error_analysis)
            if recommended_strategy:
                logger.info(f"📚 学习引擎推荐策略: {recommended_strategy['recommended_strategy']} (置信度: {recommended_strategy['confidence']:.2f})")
                
                # 记录学习推荐
                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_name="学习引擎策略推荐",
                    prompt=f"错误分析: {error_analysis}",
                    response=f"""
## 📚 学习引擎推荐

### 🎯 推荐策略
- **策略**: {recommended_strategy['recommended_strategy']}
- **置信度**: {recommended_strategy['confidence']:.2f}
- **推荐理由**: {recommended_strategy['reasoning']}
- **预估时间**: {recommended_strategy.get('estimated_time', 'N/A')}秒

### 📊 学习来源
{recommended_strategy.get('learning_source', 'unknown')}
""",
                    model_name="learning-engine",
                    duration=0.1,
                    status=ConversationStatus.SUCCESS
                )
            
            # 第2步：执行智能修复协调器
            fix_result = await self.coordinator.execute_intelligent_fix(
                error_analysis=error_analysis,
                project_path=project_path,
                session_id=session_id,
                job_info=job_info
            )
            
            if not isinstance(fix_result, dict):
                logger.error("修复结果格式错误")
                return UnifiedFixResult(
                    success=False,
                    message="修复结果格式错误",
                    strategy_used="error",
                    complexity="unknown",
                    execution_time=time.time() - start_time
                )
            
            # 第3步：执行修复验证（仅在修复成功时）
            validation_result = None
            if fix_result.get('success', False):
                logger.info("🔍 开始修复验证...")
                validation_result = await self.validator.validate_fix(
                    original_error_analysis=error_analysis,
                    fix_result=fix_result,
                    project_path=project_path,
                    session_id=session_id
                )
                
                # 记录验证结果
                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_name="智能修复验证",
                    prompt=f"验证修复结果: {fix_result}",
                    response=f"""
## 🔍 智能修复验证结果

### 📊 验证状态
- **总体结果**: {validation_result.overall_result.value}
- **成功率**: {validation_result.success_rate:.1%}
- **执行时间**: {validation_result.execution_time:.2f}秒

### 📝 验证详情
验证了 {len(validation_result.validation_details)} 项检查

### 💡 改进建议
{chr(10).join(f"- {rec}" for rec in validation_result.recommendations)}
""",
                    model_name="intelligent-validator",
                    duration=validation_result.execution_time,
                    status=ConversationStatus.SUCCESS if validation_result.overall_result.value == 'success' else ConversationStatus.FAILED
                )
            
            # 第4步：记录学习数据
            learning_recorded = False
            try:
                self.learning_engine.record_fix_attempt(
                    error_analysis=error_analysis,
                    fix_decision={
                        'strategy': fix_result.get('strategy', 'unknown'),
                        'complexity': fix_result.get('complexity', 'unknown')
                    },
                    fix_result=fix_result,
                    validation_result=validation_result.__dict__ if validation_result else None
                )
                learning_recorded = True
                logger.info("📚 学习数据记录成功")
            except Exception as e:
                logger.error(f"学习数据记录失败: {e}")
            
            # 第5步：构建统一结果
            execution_time = time.time() - start_time
            final_success = False
            
            if validation_result:
                final_success = validation_result.overall_result.value == 'success'
            else:
                final_success = fix_result.get('success', False)
            
            result = UnifiedFixResult(
                success=final_success,
                message=self._build_comprehensive_message(fix_result, validation_result),
                strategy_used=fix_result.get('strategy', 'unknown'),
                complexity=fix_result.get('complexity', 'unknown'),
                execution_time=execution_time,
                validation_result=validation_result.__dict__ if validation_result else None,
                learning_recorded=learning_recorded,
                detailed_data=fix_result
            )
            
            logger.info(f"✅ 统一智能修复完成: {result.strategy_used} ({'成功' if result.success else '失败'})")
            return result
            
        except Exception as e:
            logger.error(f"统一智能修复引擎执行失败: {e}")
            return UnifiedFixResult(
                success=False,
                message=f"统一智能修复引擎执行失败: {str(e)}",
                strategy_used="error",
                complexity="unknown",
                execution_time=time.time() - start_time if 'start_time' in locals() else 0
            )
    
    def _build_comprehensive_message(self, fix_result: Dict[str, Any], validation_result) -> str:
        """构建综合消息"""
        try:
            strategy = fix_result.get('strategy', 'unknown')
            complexity = fix_result.get('complexity', 'unknown')
            
            message_parts = [
                f"🎯 修复策略: {strategy}",
                f"🧠 复杂度: {complexity}",
                f"⏱️ 执行时间: {fix_result.get('execution_time', 0):.2f}秒"
            ]
            
            if fix_result.get('success'):
                message_parts.append("✅ 修复执行成功")
            else:
                message_parts.append("❌ 修复执行失败")
            
            if validation_result:
                message_parts.append(f"🔍 验证结果: {validation_result.overall_result.value}")
                message_parts.append(f"📊 验证成功率: {validation_result.success_rate:.1%}")
            
            # 添加具体的修复信息
            if strategy == 'automated':
                if 'command_results' in fix_result:
                    successful_commands = sum(1 for cmd in fix_result['command_results'] if cmd.get('success'))
                    total_commands = len(fix_result['command_results'])
                    message_parts.append(f"🤖 自动化命令: {successful_commands}/{total_commands} 成功")
            
            elif strategy == 'aider_guided':
                if 'aider_result' in fix_result:
                    aider_result = fix_result['aider_result']
                    message_parts.append(f"🧠 Aider修复: {aider_result.get('message', '已完成')}")
            
            elif strategy == 'automated_fallback_to_aider':
                message_parts.append("🔄 自动化失败后切换到Aider修复")
            
            return " | ".join(message_parts)
            
        except Exception as e:
            logger.error(f"构建综合消息失败: {e}")
            return f"修复完成，详情: {fix_result.get('message', '无详细信息')}"
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """获取学习统计信息"""
        return self.learning_engine.get_learning_statistics()
    
    async def analyze_fix_patterns(self, error_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """分析修复模式"""
        try:
            # 获取推荐策略
            recommended = self.learning_engine.get_recommended_strategy(error_analysis)
            
            # 获取学习统计
            stats = self.learning_engine.get_learning_statistics()
            
            return {
                'recommended_strategy': recommended,
                'learning_statistics': stats,
                'error_signature': self.learning_engine.generate_error_signature(error_analysis)
            }
            
        except Exception as e:
            logger.error(f"分析修复模式失败: {e}")
            return {'error': str(e)}


# 全局统一智能修复引擎实例
global_unified_intelligent_fix_engine = UnifiedIntelligentFixEngine()
