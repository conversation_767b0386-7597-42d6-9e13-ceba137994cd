#!/usr/bin/env python3
"""
测试Web UI执行链路分类修复效果
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_detection_logic():
    """测试AI检测逻辑"""
    print("🧪 测试AI检测逻辑修复效果...")
    
    # 模拟会话数据
    test_rounds = [
        {
            "round_number": 2,
            "round_name": "第2轮：智能修复路由决策",
            "model_name": "deepseek/deepseek-r1:free",
            "duration": 23.4,
            "token_usage": None,
            "status": "success"
        },
        {
            "round_number": 3,
            "round_name": "第3轮：自动化修复命令 1",
            "model_name": "automated-executor",
            "duration": 6.2,
            "token_usage": None,
            "status": "failed"
        },
        {
            "round_number": 4,
            "round_name": "第4轮：修复效果验证",
            "model_name": "deepseek/deepseek-r1:free",
            "duration": 8.1,
            "token_usage": None,
            "status": "failed"
        }
    ]
    
    # 系统处理器列表
    system_processors = [
        'intelligent-job-analyzer', 'fix-verifier', 'second-round-precise-fixer',
        'second-round-fixer', 'job-analyzer', 'error-analyzer', 'fix-executor',
        'automated-executor', 'intelligent-fix-router', 'aider-coordinator',
        'aider-executor', 'aider-monitor', 'rule-based-fallback'
    ]
    
    print("\n📊 测试结果:")
    print("=" * 60)
    
    for round_data in test_rounds:
        model_name = round_data.get('model_name', '')
        token_usage = round_data.get('token_usage')
        duration = round_data.get('duration', 0)
        
        # 应用修复后的判断逻辑
        is_real_ai = False
        
        if model_name not in system_processors:
            is_real_ai = (
                # 包含AI服务提供商名称
                any(provider in model_name.lower() for provider in ['deepseek', 'gpt', 'claude', 'openai', 'anthropic']) or
                # 包含模型路径格式 (如 deepseek/deepseek-r1:free)
                '/' in model_name or
                # 标准模型名称格式
                model_name.startswith(('gpt-', 'claude-', 'deepseek-')) or
                # 有token使用记录
                token_usage is not None
                # 移除了执行时间判断
            )
        
        # 判断类型
        step_type = "ai" if is_real_ai else "automated"
        
        print(f"轮次 {round_data['round_number']}: {round_data['round_name']}")
        print(f"  模型名称: {model_name}")
        print(f"  执行时间: {duration}秒")
        print(f"  是否在系统处理器列表: {model_name in system_processors}")
        print(f"  判断结果: {'🤖 AI接口调用' if is_real_ai else '⚙️ 系统处理器'}")
        print(f"  步骤类型: {step_type}")
        print("-" * 40)
    
    # 验证修复效果
    print("\n✅ 修复验证:")
    
    # 第2轮应该是AI调用
    round2 = test_rounds[0]
    model_name = round2['model_name']
    is_ai = model_name not in system_processors and (
        any(provider in model_name.lower() for provider in ['deepseek', 'gpt', 'claude', 'openai', 'anthropic']) or
        '/' in model_name or
        model_name.startswith(('gpt-', 'claude-', 'deepseek-'))
    )
    print(f"第2轮 (deepseek/deepseek-r1:free): {'✅ 正确识别为AI调用' if is_ai else '❌ 错误识别'}")
    
    # 第3轮应该是系统处理器
    round3 = test_rounds[1]
    model_name = round3['model_name']
    is_system = model_name in system_processors
    print(f"第3轮 (automated-executor): {'✅ 正确识别为系统处理器' if is_system else '❌ 错误识别'}")
    
    # 第4轮应该是AI调用
    round4 = test_rounds[2]
    model_name = round4['model_name']
    is_ai = model_name not in system_processors and (
        any(provider in model_name.lower() for provider in ['deepseek', 'gpt', 'claude', 'openai', 'anthropic']) or
        '/' in model_name or
        model_name.startswith(('gpt-', 'claude-', 'deepseek-'))
    )
    print(f"第4轮 (deepseek/deepseek-r1:free): {'✅ 正确识别为AI调用' if is_ai else '❌ 错误识别'}")
    
    return True

if __name__ == "__main__":
    success = test_ai_detection_logic()
    if success:
        print("\n🎉 Web UI AI检测逻辑修复测试通过!")
    else:
        print("\n💥 测试失败!")
        sys.exit(1)
