"""
多项目管理器 - 支持多项目和团队协作

核心功能：
1. 多项目配置管理
2. 团队协作和权限控制
3. 修复策略共享
4. 项目模板和最佳实践
"""

import logging
import json
import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import time

logger = logging.getLogger(__name__)

class ProjectType(Enum):
    """项目类型"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    GO = "go"
    RUST = "rust"
    MIXED = "mixed"

class UserRole(Enum):
    """用户角色"""
    ADMIN = "admin"           # 管理员
    MAINTAINER = "maintainer" # 维护者
    DEVELOPER = "developer"   # 开发者
    VIEWER = "viewer"        # 查看者

@dataclass
class ProjectConfig:
    """项目配置"""
    project_id: str
    name: str
    path: str
    type: ProjectType
    description: str
    owner: str
    team_members: List[Dict[str, str]]  # [{"user": "username", "role": "role"}]
    fix_strategies: Dict[str, Any]
    quality_standards: Dict[str, Any]
    ci_integration: Dict[str, Any]
    created_at: float
    updated_at: float
    active: bool = True

@dataclass
class TeamTemplate:
    """团队模板"""
    template_id: str
    name: str
    description: str
    project_type: ProjectType
    fix_strategies: Dict[str, Any]
    quality_standards: Dict[str, Any]
    tools_config: Dict[str, Any]
    created_by: str
    created_at: float
    usage_count: int = 0

class MultiProjectManager:
    """多项目管理器"""
    
    def __init__(self, config_path: str = "data/projects"):
        self.config_path = config_path
        self.projects = {}
        self.templates = {}
        self.user_permissions = {}
        self.shared_strategies = {}
        
        # 确保配置目录存在
        os.makedirs(config_path, exist_ok=True)
        
        # 加载配置
        self.load_configurations()
    
    def load_configurations(self):
        """加载配置"""
        try:
            # 加载项目配置
            projects_file = os.path.join(self.config_path, "projects.json")
            if os.path.exists(projects_file):
                with open(projects_file, 'r', encoding='utf-8') as f:
                    projects_data = json.load(f)
                    for project_id, project_data in projects_data.items():
                        self.projects[project_id] = ProjectConfig(**project_data)
            
            # 加载模板
            templates_file = os.path.join(self.config_path, "templates.json")
            if os.path.exists(templates_file):
                with open(templates_file, 'r', encoding='utf-8') as f:
                    templates_data = json.load(f)
                    for template_id, template_data in templates_data.items():
                        self.templates[template_id] = TeamTemplate(**template_data)
            
            # 加载用户权限
            permissions_file = os.path.join(self.config_path, "permissions.json")
            if os.path.exists(permissions_file):
                with open(permissions_file, 'r', encoding='utf-8') as f:
                    self.user_permissions = json.load(f)
            
            # 加载共享策略
            strategies_file = os.path.join(self.config_path, "shared_strategies.json")
            if os.path.exists(strategies_file):
                with open(strategies_file, 'r', encoding='utf-8') as f:
                    self.shared_strategies = json.load(f)
            
            logger.info(f"✅ 加载了 {len(self.projects)} 个项目配置")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
    
    def save_configurations(self):
        """保存配置"""
        try:
            # 保存项目配置
            projects_data = {pid: asdict(project) for pid, project in self.projects.items()}
            with open(os.path.join(self.config_path, "projects.json"), 'w', encoding='utf-8') as f:
                json.dump(projects_data, f, ensure_ascii=False, indent=2)
            
            # 保存模板
            templates_data = {tid: asdict(template) for tid, template in self.templates.items()}
            with open(os.path.join(self.config_path, "templates.json"), 'w', encoding='utf-8') as f:
                json.dump(templates_data, f, ensure_ascii=False, indent=2)
            
            # 保存用户权限
            with open(os.path.join(self.config_path, "permissions.json"), 'w', encoding='utf-8') as f:
                json.dump(self.user_permissions, f, ensure_ascii=False, indent=2)
            
            # 保存共享策略
            with open(os.path.join(self.config_path, "shared_strategies.json"), 'w', encoding='utf-8') as f:
                json.dump(self.shared_strategies, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def create_project(self, 
                      name: str,
                      path: str,
                      project_type: ProjectType,
                      owner: str,
                      description: str = "",
                      template_id: Optional[str] = None) -> str:
        """创建项目"""
        try:
            project_id = f"proj_{int(time.time() * 1000)}"
            
            # 从模板获取配置
            fix_strategies = {}
            quality_standards = {}
            
            if template_id and template_id in self.templates:
                template = self.templates[template_id]
                fix_strategies = template.fix_strategies.copy()
                quality_standards = template.quality_standards.copy()
                template.usage_count += 1
            else:
                # 使用默认配置
                fix_strategies = self._get_default_fix_strategies(project_type)
                quality_standards = self._get_default_quality_standards(project_type)
            
            project = ProjectConfig(
                project_id=project_id,
                name=name,
                path=path,
                type=project_type,
                description=description,
                owner=owner,
                team_members=[{"user": owner, "role": UserRole.ADMIN.value}],
                fix_strategies=fix_strategies,
                quality_standards=quality_standards,
                ci_integration={},
                created_at=time.time(),
                updated_at=time.time()
            )
            
            self.projects[project_id] = project
            self.save_configurations()
            
            logger.info(f"✅ 创建项目: {name} ({project_id})")
            return project_id
            
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            raise
    
    def add_team_member(self, project_id: str, user: str, role: UserRole, requester: str) -> bool:
        """添加团队成员"""
        try:
            if not self.check_permission(project_id, requester, "manage_team"):
                logger.warning(f"用户 {requester} 无权限管理团队")
                return False
            
            if project_id not in self.projects:
                logger.error(f"项目不存在: {project_id}")
                return False
            
            project = self.projects[project_id]
            
            # 检查用户是否已存在
            for member in project.team_members:
                if member["user"] == user:
                    member["role"] = role.value  # 更新角色
                    break
            else:
                # 添加新成员
                project.team_members.append({"user": user, "role": role.value})
            
            project.updated_at = time.time()
            self.save_configurations()
            
            logger.info(f"✅ 添加团队成员: {user} -> {project.name} ({role.value})")
            return True
            
        except Exception as e:
            logger.error(f"添加团队成员失败: {e}")
            return False
    
    def check_permission(self, project_id: str, user: str, action: str) -> bool:
        """检查用户权限"""
        try:
            if project_id not in self.projects:
                return False
            
            project = self.projects[project_id]
            
            # 查找用户角色
            user_role = None
            for member in project.team_members:
                if member["user"] == user:
                    user_role = UserRole(member["role"])
                    break
            
            if not user_role:
                return False
            
            # 权限检查
            permissions = {
                UserRole.ADMIN: ["manage_team", "modify_config", "delete_project", "view", "fix"],
                UserRole.MAINTAINER: ["modify_config", "view", "fix"],
                UserRole.DEVELOPER: ["view", "fix"],
                UserRole.VIEWER: ["view"]
            }
            
            return action in permissions.get(user_role, [])
            
        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            return False
    
    def get_project_config(self, project_id: str, user: str) -> Optional[ProjectConfig]:
        """获取项目配置"""
        if not self.check_permission(project_id, user, "view"):
            return None
        
        return self.projects.get(project_id)
    
    def update_fix_strategies(self, 
                            project_id: str, 
                            strategies: Dict[str, Any], 
                            user: str) -> bool:
        """更新修复策略"""
        try:
            if not self.check_permission(project_id, user, "modify_config"):
                return False
            
            if project_id not in self.projects:
                return False
            
            project = self.projects[project_id]
            project.fix_strategies.update(strategies)
            project.updated_at = time.time()
            
            self.save_configurations()
            
            logger.info(f"✅ 更新修复策略: {project.name}")
            return True
            
        except Exception as e:
            logger.error(f"更新修复策略失败: {e}")
            return False
    
    def share_strategy(self, 
                      strategy_name: str,
                      strategy_config: Dict[str, Any],
                      project_type: ProjectType,
                      shared_by: str,
                      description: str = "") -> str:
        """分享修复策略"""
        try:
            strategy_id = f"strategy_{int(time.time() * 1000)}"
            
            self.shared_strategies[strategy_id] = {
                'name': strategy_name,
                'config': strategy_config,
                'project_type': project_type.value,
                'shared_by': shared_by,
                'description': description,
                'created_at': time.time(),
                'usage_count': 0,
                'ratings': []
            }
            
            self.save_configurations()
            
            logger.info(f"✅ 分享策略: {strategy_name}")
            return strategy_id
            
        except Exception as e:
            logger.error(f"分享策略失败: {e}")
            raise
    
    def get_shared_strategies(self, project_type: Optional[ProjectType] = None) -> List[Dict[str, Any]]:
        """获取共享策略"""
        strategies = []
        
        for strategy_id, strategy in self.shared_strategies.items():
            if project_type is None or strategy['project_type'] == project_type.value:
                strategy_info = strategy.copy()
                strategy_info['id'] = strategy_id
                strategies.append(strategy_info)
        
        # 按使用次数排序
        strategies.sort(key=lambda x: x['usage_count'], reverse=True)
        
        return strategies
    
    def create_template(self, 
                       name: str,
                       project_type: ProjectType,
                       fix_strategies: Dict[str, Any],
                       quality_standards: Dict[str, Any],
                       tools_config: Dict[str, Any],
                       created_by: str,
                       description: str = "") -> str:
        """创建团队模板"""
        try:
            template_id = f"template_{int(time.time() * 1000)}"
            
            template = TeamTemplate(
                template_id=template_id,
                name=name,
                description=description,
                project_type=project_type,
                fix_strategies=fix_strategies,
                quality_standards=quality_standards,
                tools_config=tools_config,
                created_by=created_by,
                created_at=time.time()
            )
            
            self.templates[template_id] = template
            self.save_configurations()
            
            logger.info(f"✅ 创建模板: {name}")
            return template_id
            
        except Exception as e:
            logger.error(f"创建模板失败: {e}")
            raise
    
    def get_user_projects(self, user: str) -> List[Dict[str, Any]]:
        """获取用户项目列表"""
        user_projects = []
        
        for project_id, project in self.projects.items():
            if not project.active:
                continue
                
            # 检查用户是否是团队成员
            user_role = None
            for member in project.team_members:
                if member["user"] == user:
                    user_role = member["role"]
                    break
            
            if user_role:
                user_projects.append({
                    'project_id': project_id,
                    'name': project.name,
                    'type': project.type.value,
                    'role': user_role,
                    'description': project.description,
                    'updated_at': project.updated_at
                })
        
        # 按更新时间排序
        user_projects.sort(key=lambda x: x['updated_at'], reverse=True)
        
        return user_projects
    
    def _get_default_fix_strategies(self, project_type: ProjectType) -> Dict[str, Any]:
        """获取默认修复策略"""
        base_strategies = {
            'error_classification': {
                'enabled': True,
                'priority_weights': {
                    'critical': 1.0,
                    'high': 0.8,
                    'medium': 0.6,
                    'low': 0.4
                }
            },
            'automated_fixes': {
                'enabled': True,
                'max_attempts': 3,
                'timeout': 300
            },
            'ai_fixes': {
                'enabled': True,
                'model': 'deepseek/deepseek-coder',
                'max_context_files': 10
            }
        }
        
        # 根据项目类型添加特定策略
        if project_type == ProjectType.PYTHON:
            base_strategies.update({
                'python_specific': {
                    'use_black': True,
                    'use_flake8': True,
                    'use_mypy': False
                }
            })
        
        return base_strategies
    
    def _get_default_quality_standards(self, project_type: ProjectType) -> Dict[str, Any]:
        """获取默认质量标准"""
        base_standards = {
            'complexity_threshold': 10,
            'line_length': 88,
            'function_length': 50,
            'test_coverage': 80
        }
        
        if project_type == ProjectType.PYTHON:
            base_standards.update({
                'python_version': '3.8+',
                'docstring_required': True,
                'type_hints_required': False
            })
        
        return base_standards


# 全局多项目管理器实例
global_multi_project_manager = MultiProjectManager()
