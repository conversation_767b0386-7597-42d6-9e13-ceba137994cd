{"file_creation": [{"description": "文件创建模式", "pattern": "文件创建模式", "context": "创建用户认证API", "recorded_at": "2025-05-27T15:06:05.246201", "usage_count": 148, "last_used": "2025-05-31T11:41:34.604396"}], "api_design": [{"name": "RESTful API设计模式", "description": "使用标准的REST API设计原则", "template": "GET /api/v1/resources, POST /api/v1/resources", "usage_count": 1, "recorded_at": "2025-05-27T15:23:37.022752"}], "error_handling": [{"name": "统一错误处理模式", "description": "使用中间件进行全局异常处理", "template": "try-except + 自定义异常类", "usage_count": 1, "recorded_at": "2025-05-27T15:23:37.035264"}], "directory_structure": [{"description": "目录结构组织模式", "pattern": "目录结构模式", "context": "以后 python 镜像都用这个吧速度快很多 ***************:5050/docker...", "recorded_at": "2025-05-27T15:49:03.878185", "usage_count": 82, "last_used": "2025-05-31T11:41:34.616858"}]}